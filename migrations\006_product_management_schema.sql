-- =====================================================
-- PRODUCT MANAGEMENT SYSTEM DATABASE SCHEMA
-- =====================================================
-- ## DATABASE LATER: Execute this migration when implementing Supabase backend
-- ## TODO: Review and adjust schema based on final requirements

-- Enable UUID extension if not already enabled
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- PRODUCTS TABLE
-- =====================================================
-- ## DATABASE LATER: Main products table
/*
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  
  -- Basic Information
  name TEXT NOT NULL,
  name_english TEXT,
  description TEXT,
  description_english TEXT,
  category TEXT NOT NULL,
  
  -- Media and Pricing
  image_url TEXT, -- ## TODO: Supabase Storage integration
  base_price DECIMAL(10,2), -- Base price in USD
  estimated_time TEXT, -- "فوري", "5-10 دقائق", etc.
  
  -- Product Configuration
  product_type TEXT NOT NULL CHECK (product_type IN ('physical', 'digital', 'service')),
  processing_type TEXT NOT NULL CHECK (processing_type IN ('instant', 'manual')),
  
  -- Digital Product Configuration (JSONB for flexibility)
  digital_config JSONB, -- {autoDeliver: boolean, codeType: string, etc.}
  
  -- Display and Features
  features TEXT[], -- Array of feature strings in Arabic
  tags TEXT[], -- Search tags
  is_active BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id), -- ## TODO: Admin user reference
  
  -- Indexes for performance
  CONSTRAINT products_name_check CHECK (char_length(name) >= 2),
  CONSTRAINT products_category_check CHECK (char_length(category) >= 2)
);

-- Create indexes for better query performance
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_type ON products(product_type);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_products_featured ON products(is_featured);
CREATE INDEX idx_products_created_at ON products(created_at);
*/

-- =====================================================
-- PRODUCT PACKAGES TABLE
-- =====================================================
-- ## DATABASE LATER: Product packages/options table
/*
CREATE TABLE product_packages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  
  -- Package Information
  name TEXT NOT NULL, -- "60 UC", "325 UC"
  name_arabic TEXT NOT NULL, -- "60 يوسي", "325 يوسي"
  amount TEXT NOT NULL, -- Display amount "60 UC"
  description TEXT,
  
  -- Pricing
  price DECIMAL(10,2) NOT NULL, -- Price in USD
  original_price DECIMAL(10,2), -- For discount display
  discount_percentage INTEGER, -- Discount %
  
  -- Configuration
  is_popular BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT packages_price_positive CHECK (price > 0),
  CONSTRAINT packages_discount_valid CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
  CONSTRAINT packages_name_check CHECK (char_length(name) >= 1)
);

-- Create indexes
CREATE INDEX idx_packages_product_id ON product_packages(product_id);
CREATE INDEX idx_packages_active ON product_packages(is_active);
CREATE INDEX idx_packages_sort_order ON product_packages(sort_order);
*/

-- =====================================================
-- CUSTOM FIELDS TABLE
-- =====================================================
-- ## DATABASE LATER: Dynamic form fields configuration
/*
CREATE TABLE custom_fields (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  
  -- Field Configuration
  field_type TEXT NOT NULL CHECK (field_type IN ('text', 'number', 'email', 'dropdown', 'package', 'quantity', 'textarea', 'image')),
  name TEXT NOT NULL, -- Internal field name
  label TEXT NOT NULL, -- Display label in Arabic
  label_english TEXT, -- English label for admin
  placeholder TEXT, -- Placeholder text in Arabic
  
  -- Validation Rules (JSONB for flexibility)
  validation JSONB, -- {required: boolean, minLength: number, pattern: string, etc.}
  options TEXT[], -- For dropdown fields
  depends_on TEXT, -- Field dependency
  
  -- Configuration
  is_required BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT fields_name_unique_per_product UNIQUE(product_id, name),
  CONSTRAINT fields_label_check CHECK (char_length(label) >= 1),
  CONSTRAINT fields_name_check CHECK (char_length(name) >= 1)
);

-- Create indexes
CREATE INDEX idx_custom_fields_product_id ON custom_fields(product_id);
CREATE INDEX idx_custom_fields_type ON custom_fields(field_type);
CREATE INDEX idx_custom_fields_active ON custom_fields(is_active);
CREATE INDEX idx_custom_fields_sort_order ON custom_fields(sort_order);
*/

-- =====================================================
-- ENCRYPTED CODES TABLE
-- =====================================================
-- ## DATABASE LATER: Digital product codes storage
/*
CREATE TABLE encrypted_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  package_id UUID NOT NULL REFERENCES product_packages(id) ON DELETE CASCADE,
  
  -- Code Information
  code_encrypted TEXT NOT NULL, -- ## TODO: Implement encryption/decryption
  code_type TEXT NOT NULL CHECK (code_type IN ('game_code', 'coupon', 'license', 'download_link', 'credentials')),
  instructions TEXT, -- How to use the code
  
  -- Status and Delivery
  is_used BOOLEAN DEFAULT false,
  is_reserved BOOLEAN DEFAULT false, -- Reserved for pending order
  order_id UUID, -- ## TODO: Reference to orders table
  delivered_at TIMESTAMP WITH TIME ZONE,
  accessed_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id), -- Admin who added the code
  
  -- Constraints
  CONSTRAINT codes_not_empty CHECK (char_length(code_encrypted) >= 1)
);

-- Create indexes
CREATE INDEX idx_encrypted_codes_package_id ON encrypted_codes(package_id);
CREATE INDEX idx_encrypted_codes_used ON encrypted_codes(is_used);
CREATE INDEX idx_encrypted_codes_reserved ON encrypted_codes(is_reserved);
CREATE INDEX idx_encrypted_codes_order_id ON encrypted_codes(order_id);
CREATE INDEX idx_encrypted_codes_expires_at ON encrypted_codes(expires_at);
*/

-- =====================================================
-- PRODUCT CATEGORIES TABLE (Optional)
-- =====================================================
-- ## DATABASE LATER: Predefined categories for better organization
/*
CREATE TABLE product_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  name_arabic TEXT NOT NULL,
  description TEXT,
  icon TEXT, -- Icon name or URL
  color TEXT, -- Hex color code
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_categories_active ON product_categories(is_active);
CREATE INDEX idx_categories_sort_order ON product_categories(sort_order);
*/

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================
-- ## DATABASE LATER: Implement RLS for security

-- Enable RLS on all tables
/*
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_fields ENABLE ROW LEVEL SECURITY;
ALTER TABLE encrypted_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;

-- Public read access for active products (for shop display)
CREATE POLICY "Public can view active products" ON products
  FOR SELECT USING (is_active = true);

-- Public read access for active packages
CREATE POLICY "Public can view active packages" ON product_packages
  FOR SELECT USING (is_active = true);

-- Public read access for active fields
CREATE POLICY "Public can view active fields" ON custom_fields
  FOR SELECT USING (is_active = true);

-- Admin full access (requires admin role)
CREATE POLICY "Admins can manage products" ON products
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admins can manage packages" ON product_packages
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admins can manage fields" ON custom_fields
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Admins can manage codes" ON encrypted_codes
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Categories are publicly readable
CREATE POLICY "Public can view active categories" ON product_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage categories" ON product_categories
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');
*/

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================
-- ## DATABASE LATER: Auto-update timestamps

/*
-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to all tables
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_packages_updated_at BEFORE UPDATE ON product_packages
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fields_updated_at BEFORE UPDATE ON custom_fields
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_codes_updated_at BEFORE UPDATE ON encrypted_codes
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON product_categories
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
*/

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================
-- ## DATABASE LATER: Insert sample data for testing

/*
-- Insert sample categories
INSERT INTO product_categories (name, name_arabic, description, icon, color, sort_order) VALUES
('mobile-games', 'ألعاب الموبايل', 'شحن ألعاب الهواتف الذكية', 'smartphone', '#3B82F6', 1),
('pc-games', 'ألعاب الكمبيوتر', 'شحن ألعاب الكمبيوتر', 'monitor', '#10B981', 2),
('gift-cards', 'بطاقات الهدايا', 'بطاقات هدايا رقمية', 'gift', '#F59E0B', 3),
('subscriptions', 'الاشتراكات', 'اشتراكات التطبيقات والخدمات', 'calendar', '#8B5CF6', 4);

-- Insert sample PUBG Mobile product
INSERT INTO products (name, name_english, description, category, product_type, processing_type, estimated_time, features, is_active, is_featured) VALUES
('شحن يوسي PUBG Mobile', 'PUBG Mobile UC Top-up', 'شحن فوري لعملة UC في لعبة PUBG Mobile', 'mobile-games', 'digital', 'instant', 'فوري', 
 ARRAY['🚀 تسليم فوري', '💯 ضمان الجودة', '🔒 آمن ومضمون', '📱 لجميع الأجهزة'], true, true);
*/
