"use client"

import React, { useState } from "react"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Key, Shield, Zap, AlertTriangle } from "lucide-react"
import { PackageEditor } from "@/components/admin/PackageEditor"
import { ModernPackageGrid } from "@/components/products/ModernPackageGrid"
import { ProductPackage } from "@/lib/types"
import { generateId } from "@/lib/utils"

// Demo packages with digital codes
const initialPackages: ProductPackage[] = [
  {
    id: generateId(),
    name: "60 يوسي",
    amount: "60 UC",
    price: 5,
    originalPrice: 6,
    discount: 17,
    popular: false,
    isActive: true,
    sortOrder: 0,
    digitalCodes: [] // Will be populated via the manager
  },
  {
    id: generateId(),
    name: "325 يوسي",
    amount: "325 UC", 
    price: 25,
    originalPrice: 30,
    discount: 17,
    popular: true,
    isActive: true,
    sortOrder: 1,
    digitalCodes: []
  },
  {
    id: generateId(),
    name: "660 يوسي",
    amount: "660 UC",
    price: 50,
    originalPrice: 60,
    discount: 17,
    popular: false,
    isActive: true,
    sortOrder: 2,
    digitalCodes: []
  }
]

export default function DigitalCodesDemo() {
  const [packages, setPackages] = useState<ProductPackage[]>(initialPackages)
  const [selectedPackage, setSelectedPackage] = useState<ProductPackage | null>(null)
  const [viewMode, setViewMode] = useState<'admin' | 'customer'>('admin')

  const handlePackagesUpdate = (updatedPackages: ProductPackage[]) => {
    setPackages(updatedPackages)
  }

  const handlePackageSelect = (pkg: ProductPackage) => {
    setSelectedPackage(pkg)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900" dir="rtl">
      {/* Header */}
      <div className="sticky top-0 z-50 bg-slate-900/95 backdrop-blur-sm border-b border-slate-700">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Link href="/demo/modern-products">
              <Button
                variant="ghost"
                size="sm"
                className="text-slate-300 hover:text-white"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className="text-lg font-bold text-white">إدارة الأكواد الرقمية</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'admin' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('admin')}
              className={viewMode === 'admin' ? 'bg-blue-600' : 'border-slate-600 text-slate-300'}
            >
              <Key className="h-4 w-4 ml-1" />
              إدارة
            </Button>
            <Button
              variant={viewMode === 'customer' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('customer')}
              className={viewMode === 'customer' ? 'bg-green-600' : 'border-slate-600 text-slate-300'}
            >
              <Shield className="h-4 w-4 ml-1" />
              عميل
            </Button>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Introduction */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardContent className="p-6">
            <div className="text-center mb-4">
              <div className="text-4xl mb-3">🔐</div>
              <h2 className="text-white font-bold text-xl mb-2">
                نظام الأكواد الرقمية المشفرة
              </h2>
              <p className="text-slate-400 mb-4">
                إدارة آمنة للأكواد الرقمية مع تشفير متقدم وتتبع المخزون
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-slate-700/30 rounded-lg p-4 text-center">
                <Shield className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <h3 className="text-white font-medium mb-1">تشفير آمن</h3>
                <p className="text-slate-400 text-sm">جميع الأكواد مشفرة بتقنية AES</p>
              </div>
              <div className="bg-slate-700/30 rounded-lg p-4 text-center">
                <Key className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <h3 className="text-white font-medium mb-1">إدارة ذكية</h3>
                <p className="text-slate-400 text-sm">تتبع المخزون والاستخدام</p>
              </div>
              <div className="bg-slate-700/30 rounded-lg p-4 text-center">
                <Zap className="h-8 w-8 text-yellow-400 mx-auto mb-2" />
                <h3 className="text-white font-medium mb-1">تسليم فوري</h3>
                <p className="text-slate-400 text-sm">كشف الأكواد عند الشراء فقط</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* View Toggle Info */}
        <Card className="bg-slate-800/30 border-slate-700/50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-400/20 rounded-lg flex items-center justify-center">
                <div className="text-lg">👁️</div>
              </div>
              <div>
                <div className="text-white font-medium">
                  {viewMode === 'admin' ? 'وضع الإدارة' : 'وضع العميل'}
                </div>
                <div className="text-slate-400 text-sm">
                  {viewMode === 'admin' 
                    ? 'يمكنك إضافة وإدارة الأكواد الرقمية للحزم'
                    : 'كيف يرى العملاء الحزم مع معلومات المخزون'
                  }
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Admin View */}
        {viewMode === 'admin' && (
          <div className="space-y-6">
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  إدارة الحزم والأكواد
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
                    <h4 className="text-blue-300 font-medium mb-2">كيفية الاستخدام:</h4>
                    <ul className="text-slate-300 text-sm space-y-1">
                      <li>• انقر على أيقونة المفتاح 🔑 بجانب أي حزمة لإدارة أكوادها</li>
                      <li>• يمكنك إضافة أكواد فردية أو متعددة أو توليد أكواد عشوائية</li>
                      <li>• جميع الأكواد يتم تشفيرها تلقائياً قبل الحفظ</li>
                      <li>• يظهر تحذير عندما يقل المخزون عن 5 أكواد</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <PackageEditor
              packages={packages}
              onPackagesUpdate={handlePackagesUpdate}
            />
          </div>
        )}

        {/* Customer View */}
        {viewMode === 'customer' && (
          <div className="space-y-6">
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  عرض العميل - اختر الحزمة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                    <h4 className="text-green-300 font-medium mb-2">ما يراه العميل:</h4>
                    <ul className="text-slate-300 text-sm space-y-1">
                      <li>• عدد الأكواد المتاحة لكل حزمة</li>
                      <li>• تحذير المخزون القليل (أقل من 5 أكواد)</li>
                      <li>• الحزم نفدت منها الأكواد تظهر معطلة</li>
                      <li>• الأكواد لا تظهر إلا بعد الشراء والدفع</li>
                    </ul>
                  </div>

                  <ModernPackageGrid
                    packages={packages}
                    selectedPackage={selectedPackage}
                    onPackageSelect={handlePackageSelect}
                    currency="USD"
                  />

                  {selectedPackage && (
                    <Card className="bg-slate-700/50 border-slate-600">
                      <CardContent className="p-4">
                        <div className="text-center">
                          <h4 className="text-white font-bold mb-2">الحزمة المختارة</h4>
                          <p className="text-slate-300 mb-2">{selectedPackage.name}</p>
                          <div className="flex items-center justify-center gap-2 text-sm">
                            <Shield className="h-4 w-4 text-green-400" />
                            <span className="text-green-400">
                              {selectedPackage.digitalCodes?.length || 0} كود متاح
                            </span>
                          </div>
                          <p className="text-slate-400 text-xs mt-2">
                            💡 الأكواد ستظهر بعد إتمام عملية الشراء
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Security Features */}
        <Card className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border-blue-500/30">
          <CardContent className="p-6">
            <div className="text-center mb-4">
              <Shield className="h-12 w-12 text-blue-400 mx-auto mb-3" />
              <h3 className="text-white font-bold text-lg mb-2">مميزات الأمان</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="text-blue-300 font-medium">🔐 التشفير</h4>
                <ul className="text-slate-300 text-sm space-y-1">
                  <li>• تشفير AES-256 للأكواد</li>
                  <li>• مفاتيح تشفير آمنة</li>
                  <li>• لا يمكن قراءة الأكواد من قاعدة البيانات</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="text-purple-300 font-medium">📊 التتبع</h4>
                <ul className="text-slate-300 text-sm space-y-1">
                  <li>• تتبع استخدام كل كود</li>
                  <li>• منع الاستخدام المتكرر</li>
                  <li>• إحصائيات المخزون الفورية</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
