"use client"

import React from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Star, Zap, Shield } from "lucide-react"

const demoProducts = [
  {
    slug: "pubg-uc",
    name: "شحن يوسي PUBG Mobile",
    description: "شحن فوري لعملة UC في لعبة PUBG Mobile",
    image: "🎮",
    priceFrom: "$5",
    popular: true,
    features: ["تسليم فوري", "ضمان الجودة", "دعم 24/7"]
  },
  {
    slug: "free-fire",
    name: "شحن جواهر Free Fire",
    description: "شحن فوري لجواهر Free Fire بأفضل الأسعار",
    image: "💎",
    priceFrom: "$10",
    popular: false,
    features: ["شحن مباشر", "جواهر أصلية", "آمن ومضمون"]
  }
]

export default function ModernProductsDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900" dir="rtl">
      {/* Header */}
      <div className="sticky top-0 z-50 bg-slate-900/95 backdrop-blur-sm border-b border-slate-700">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Link href="/">
              <Button
                variant="ghost"
                size="sm"
                className="text-slate-300 hover:text-white"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <h1 className="text-lg font-bold text-white">تجربة التصميم الجديد</h1>
          </div>
          <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
            <Zap className="h-3 w-3 ml-1" />
            جديد
          </Badge>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Introduction */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <div className="text-4xl mb-4">🚀</div>
            <h2 className="text-white font-bold text-xl mb-2">
              التصميم الجديد لصفحات المنتجات
            </h2>
            <p className="text-slate-400 mb-4">
              تصميم حديث ومحسن خصيصاً للاعبين في منطقة الشرق الأوسط وشمال أفريقيا
            </p>
            <div className="grid grid-cols-3 gap-4 mt-6">
              <div className="text-center">
                <div className="flex justify-center mb-2">
                  <Zap className="h-6 w-6 text-yellow-400" />
                </div>
                <div className="text-white font-medium text-sm">سريع وسهل</div>
                <div className="text-slate-400 text-xs">تجربة شراء مبسطة</div>
              </div>
              <div className="text-center">
                <div className="flex justify-center mb-2">
                  <Shield className="h-6 w-6 text-blue-400" />
                </div>
                <div className="text-white font-medium text-sm">آمن ومضمون</div>
                <div className="text-slate-400 text-xs">حماية كاملة للبيانات</div>
              </div>
              <div className="text-center">
                <div className="flex justify-center mb-2">
                  <Star className="h-6 w-6 text-purple-400" />
                </div>
                <div className="text-white font-medium text-sm">تجربة مميزة</div>
                <div className="text-slate-400 text-xs">تصميم متجاوب ومتطور</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Features */}
        <div className="space-y-3">
          <h3 className="text-white font-bold text-lg">المميزات الجديدة</h3>
          <div className="grid grid-cols-1 gap-3">
            <Card className="bg-slate-800/30 border-slate-700/50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-yellow-400/20 rounded-lg flex items-center justify-center">
                    <div className="text-lg">📦</div>
                  </div>
                  <div>
                    <div className="text-white font-medium">شبكة حزم محسنة</div>
                    <div className="text-slate-400 text-sm">عرض أفضل للحزم مع إبراز العروض الشائعة</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/30 border-slate-700/50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-400/20 rounded-lg flex items-center justify-center">
                    <div className="text-lg">📝</div>
                  </div>
                  <div>
                    <div className="text-white font-medium">نموذج معلومات ذكي</div>
                    <div className="text-slate-400 text-sm">حقول ديناميكية مع رموز توضيحية وتحقق فوري</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/30 border-slate-700/50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-400/20 rounded-lg flex items-center justify-center">
                    <div className="text-lg">🛒</div>
                  </div>
                  <div>
                    <div className="text-white font-medium">أزرار شراء محسنة</div>
                    <div className="text-slate-400 text-sm">أزرار ثابتة في الأسفل مع ملخص سعر واضح</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/30 border-slate-700/50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-purple-400/20 rounded-lg flex items-center justify-center">
                    <div className="text-lg">📱</div>
                  </div>
                  <div>
                    <div className="text-white font-medium">محسن للموبايل</div>
                    <div className="text-slate-400 text-sm">تصميم متجاوب مع التركيز على تجربة الموبايل</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Demo Products */}
        <div className="space-y-3">
          <h3 className="text-white font-bold text-lg">جرب التصميم الجديد</h3>
          <div className="grid grid-cols-1 gap-4">
            {demoProducts.map((product) => (
              <Card key={product.slug} className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm overflow-hidden">
                <CardContent className="p-0">
                  <div className="flex">
                    {/* Product Image */}
                    <div className="w-20 h-20 bg-gradient-to-br from-slate-700 to-slate-600 flex items-center justify-center">
                      <div className="text-2xl">{product.image}</div>
                    </div>
                    
                    {/* Product Details */}
                    <div className="flex-1 p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h4 className="text-white font-bold text-lg mb-1">{product.name}</h4>
                          <p className="text-slate-400 text-sm mb-2">{product.description}</p>
                        </div>
                        {product.popular && (
                          <Badge className="bg-yellow-500/20 text-yellow-400 text-xs">
                            شائع
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-xs">
                          <div className="flex items-center gap-1 text-yellow-400">
                            <Star className="h-3 w-3 fill-current" />
                            <span>4.9</span>
                          </div>
                          <span className="text-slate-500">من {product.priceFrom}</span>
                        </div>
                        
                        <Link href={`/products/modern/${product.slug}`}>
                          <Button 
                            size="sm"
                            className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold"
                          >
                            جرب الآن
                          </Button>
                        </Link>
                      </div>
                      
                      <div className="flex gap-2 mt-3">
                        {product.features.map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs border-slate-600 text-slate-400">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <Card className="bg-gradient-to-r from-yellow-400/10 to-orange-500/10 border-yellow-400/30">
          <CardContent className="p-6 text-center">
            <div className="text-3xl mb-3">✨</div>
            <h3 className="text-white font-bold text-lg mb-2">
              ما رأيك في التصميم الجديد؟
            </h3>
            <p className="text-slate-300 text-sm mb-4">
              جرب المنتجات أعلاه وشاركنا رأيك في التحسينات
            </p>
            <div className="flex gap-3 justify-center">
              <Button 
                variant="outline" 
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                💬 تواصل معنا
              </Button>
              <Button 
                size="sm"
                className="bg-yellow-400 hover:bg-yellow-500 text-slate-900 font-bold"
              >
                🔄 العودة للتصميم القديم
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
