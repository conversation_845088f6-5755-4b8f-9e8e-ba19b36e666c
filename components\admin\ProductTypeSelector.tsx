"use client"

import React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Zap, 
  Key, 
  ArrowRight, 
  Clock, 
  Shield,
  Users,
  Gift,
  CreditCard
} from "lucide-react"
import { ProductDeliveryType } from "@/lib/types"

interface ProductTypeSelectorProps {
  selectedType: ProductDeliveryType
  onTypeChange: (type: ProductDeliveryType) => void
  disabled?: boolean
}

export function ProductTypeSelector({ 
  selectedType, 
  onTypeChange, 
  disabled = false 
}: ProductTypeSelectorProps) {

  const productTypes = [
    {
      type: "direct_charge" as ProductDeliveryType,
      title: "الشحن المباشر",
      subtitle: "Direct Charging",
      description: "شحن مباشر إلى حساب اللاعب باستخدام المعرف",
      icon: <Zap className="h-8 w-8" />,
      color: "from-blue-500 to-cyan-500",
      features: [
        { icon: <Clock className="h-4 w-4" />, text: "تسليم فوري" },
        { icon: <Users className="h-4 w-4" />, text: "يتطلب معرف اللاعب" },
        { icon: <Shield className="h-4 w-4" />, text: "آمن ومضمون" }
      ],
      examples: "PUBG UC, Free Fire Diamonds, Call of Duty CP",
      workflow: [
        "العميل يدخل معرف اللاعب",
        "يتم الدفع",
        "الشحن المباشر للحساب",
        "تأكيد فوري"
      ]
    },
    {
      type: "code_based" as ProductDeliveryType,
      title: "الأكواد الرقمية",
      subtitle: "Code-Based Products",
      description: "أكواد مُعدة مسبقاً يتم تسليمها للعميل",
      icon: <Key className="h-8 w-8" />,
      color: "from-purple-500 to-pink-500",
      features: [
        { icon: <Gift className="h-4 w-4" />, text: "أكواد جاهزة" },
        { icon: <CreditCard className="h-4 w-4" />, text: "بطاقات هدايا" },
        { icon: <Shield className="h-4 w-4" />, text: "مشفرة وآمنة" }
      ],
      examples: "Google Play Cards, iTunes Cards, Steam Codes",
      workflow: [
        "العميل يختار المنتج",
        "يتم الدفع",
        "تخصيص كود من المخزون",
        "العميل يكشف الكود"
      ]
    }
  ]

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-white font-bold text-lg mb-2">اختر نوع المنتج</h3>
        <p className="text-slate-400 text-sm">
          حدد كيفية تسليم المنتج للعملاء
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {productTypes.map((productType) => {
          const isSelected = selectedType === productType.type
          
          return (
            <Card
              key={productType.type}
              className={`cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                isSelected
                  ? 'border-2 border-yellow-400 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 shadow-xl shadow-yellow-400/20'
                  : 'border border-slate-600 bg-slate-800/50 hover:border-slate-500'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => !disabled && onTypeChange(productType.type)}
            >
              <CardContent className="p-6">
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${productType.color}`}>
                    <div className="text-white">
                      {productType.icon}
                    </div>
                  </div>
                  
                  {isSelected && (
                    <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-slate-900 rounded-full"></div>
                    </div>
                  )}
                </div>

                {/* Title */}
                <div className="mb-4">
                  <h4 className="text-white font-bold text-lg">{productType.title}</h4>
                  <p className="text-slate-400 text-sm">{productType.subtitle}</p>
                  <p className="text-slate-300 text-sm mt-2">{productType.description}</p>
                </div>

                {/* Features */}
                <div className="space-y-2 mb-4">
                  {productType.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-slate-300 text-sm">
                      <div className="text-green-400">
                        {feature.icon}
                      </div>
                      <span>{feature.text}</span>
                    </div>
                  ))}
                </div>

                {/* Examples */}
                <div className="mb-4">
                  <p className="text-slate-400 text-xs mb-1">أمثلة:</p>
                  <p className="text-slate-300 text-sm">{productType.examples}</p>
                </div>

                {/* Workflow */}
                <div className="space-y-2">
                  <p className="text-slate-400 text-xs">سير العمل:</p>
                  <div className="space-y-1">
                    {productType.workflow.map((step, index) => (
                      <div key={index} className="flex items-center gap-2 text-slate-300 text-xs">
                        <div className="w-4 h-4 bg-slate-600 rounded-full flex items-center justify-center text-white text-xs">
                          {index + 1}
                        </div>
                        <span>{step}</span>
                        {index < productType.workflow.length - 1 && (
                          <ArrowRight className="h-3 w-3 text-slate-500" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Selection Indicator */}
                {isSelected && (
                  <div className="mt-4 pt-4 border-t border-yellow-400/30">
                    <div className="flex items-center justify-center gap-2 text-yellow-400 text-sm font-medium">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                      <span>محدد</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Selected Type Summary */}
      {selectedType && (
        <div className="mt-6 p-4 bg-slate-700/50 rounded-lg border border-slate-600">
          <div className="flex items-center gap-3">
            <div className="text-green-400">
              <Shield className="h-5 w-5" />
            </div>
            <div>
              <p className="text-white font-medium">
                تم اختيار: {productTypes.find(t => t.type === selectedType)?.title}
              </p>
              <p className="text-slate-400 text-sm">
                {selectedType === "direct_charge" 
                  ? "سيتم شحن المنتج مباشرة إلى حساب العميل"
                  : "سيتم تسليم كود رقمي للعميل من المخزون المتاح"
                }
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
