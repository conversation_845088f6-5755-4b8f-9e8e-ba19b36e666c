// =====================================================
// PRODUCT MANAGEMENT SERVICE
// =====================================================
// ## TODO: Implement Supabase integration for all functions
// ## DATABASE LATER: Connect to products, packages, custom_fields tables

import { ProductTemplate, ProductPackage, DynamicField, ProductFilters, ProductStats } from '@/lib/types'
import { initializeDefaultTemplates, loadProductTemplates, saveProductTemplate, deleteProductTemplate } from '@/lib/data/defaultProductTemplates'

// =====================================================
// PRODUCT CRUD OPERATIONS
// =====================================================

/**
 * ## TODO: Implement Supabase product fetching
 * Fetch all products with optional filtering
 */
export async function getProducts(filters?: ProductFilters): Promise<ProductTemplate[]> {
  // ## TODO: Replace with Supabase query
  /*
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      product_packages(*),
      custom_fields(*)
    `)
    .eq('is_active', filters?.isActive ?? true)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data.map(transformProductFromDB)
  */
  
  // Temporary: Load from localStorage with default initialization
  try {
    initializeDefaultTemplates()
    const products = loadProductTemplates()
    const validProducts = Array.isArray(products) ? products : []
    return applyFilters(validProducts, filters)
  } catch (error) {
    console.error('Error loading products:', error)
    return []
  }
}

/**
 * ## TODO: Implement Supabase product fetching by ID
 * Fetch single product by ID
 */
export async function getProductById(id: string): Promise<ProductTemplate | null> {
  // ## TODO: Replace with Supabase query
  /*
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      product_packages(*),
      custom_fields(*)
    `)
    .eq('id', id)
    .single()
  
  if (error) throw error
  return transformProductFromDB(data)
  */
  
  // Temporary: Load from localStorage
  const products = await getProducts()
  return products.find(p => p.id === id) || null
}

/**
 * ## TODO: Implement Supabase product creation
 * Create new product with packages and fields
 */
export async function createProduct(product: Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProductTemplate> {
  // ## TODO: Replace with Supabase transaction
  /*
  const { data: productData, error: productError } = await supabase
    .from('products')
    .insert({
      name: product.name,
      name_english: product.nameEnglish,
      description: product.description,
      category: product.category,
      // ... other fields
    })
    .select()
    .single()
  
  if (productError) throw productError
  
  // Insert packages
  if (product.packages.length > 0) {
    const { error: packagesError } = await supabase
      .from('product_packages')
      .insert(product.packages.map(pkg => ({
        product_id: productData.id,
        name: pkg.name,
        // ... other package fields
      })))
    
    if (packagesError) throw packagesError
  }
  
  // Insert custom fields
  if (product.fields.length > 0) {
    const { error: fieldsError } = await supabase
      .from('custom_fields')
      .insert(product.fields.map(field => ({
        product_id: productData.id,
        field_type: field.type,
        // ... other field properties
      })))
    
    if (fieldsError) throw fieldsError
  }
  
  return getProductById(productData.id)
  */
  
  // Temporary: Save to localStorage
  const newProduct: ProductTemplate = {
    ...product,
    id: generateId(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
  
  saveProductTemplate(newProduct)
  
  return newProduct
}

/**
 * ## TODO: Implement Supabase product update
 * Update existing product
 */
export async function updateProduct(id: string, updates: Partial<ProductTemplate>): Promise<ProductTemplate> {
  // ## TODO: Replace with Supabase transaction
  /*
  const { data, error } = await supabase
    .from('products')
    .update({
      name: updates.name,
      description: updates.description,
      // ... other fields
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()
  
  if (error) throw error
  
  // Update packages and fields if provided
  // ... handle packages and fields updates
  
  return getProductById(id)
  */
  
  // Temporary: Update in localStorage
  const products = loadProductTemplates()
  const index = products.findIndex(p => p.id === id)
  if (index === -1) throw new Error('Product not found')

  const updatedProduct = {
    ...products[index],
    ...updates,
    updatedAt: new Date()
  }

  saveProductTemplate(updatedProduct)
  return updatedProduct
}

/**
 * ## TODO: Implement Supabase product deletion
 * Delete product and related data
 */
export async function deleteProduct(id: string): Promise<void> {
  // ## TODO: Replace with Supabase cascade delete
  /*
  const { error } = await supabase
    .from('products')
    .delete()
    .eq('id', id)
  
  if (error) throw error
  */
  
  // Temporary: Remove from localStorage
  deleteProductTemplate(id)
}

// =====================================================
// PACKAGE MANAGEMENT
// =====================================================

/**
 * ## TODO: Implement Supabase package operations
 * Get packages for a specific product
 */
export async function getProductPackages(productId: string): Promise<ProductPackage[]> {
  // ## TODO: Replace with Supabase query
  /*
  const { data, error } = await supabase
    .from('product_packages')
    .select('*')
    .eq('product_id', productId)
    .eq('is_active', true)
    .order('sort_order')
  
  if (error) throw error
  return data.map(transformPackageFromDB)
  */
  
  const product = await getProductById(productId)
  return product?.packages || []
}

/**
 * ## TODO: Implement Supabase package creation
 * Add package to product
 */
export async function addPackageToProduct(productId: string, packageData: Omit<ProductPackage, 'id'>): Promise<ProductPackage> {
  // ## TODO: Replace with Supabase insert
  /*
  const { data, error } = await supabase
    .from('product_packages')
    .insert({
      product_id: productId,
      name: packageData.name,
      // ... other fields
    })
    .select()
    .single()
  
  if (error) throw error
  return transformPackageFromDB(data)
  */
  
  const newPackage: ProductPackage = {
    ...packageData,
    id: generateId()
  }
  
  const product = await getProductById(productId)
  if (!product) throw new Error('Product not found')
  
  product.packages.push(newPackage)
  await updateProduct(productId, { packages: product.packages })
  
  return newPackage
}

// =====================================================
// STATISTICS AND ANALYTICS
// =====================================================

/**
 * ## TODO: Implement Supabase analytics queries
 * Get product statistics for admin dashboard
 */
export async function getProductStats(): Promise<ProductStats> {
  // ## TODO: Replace with Supabase aggregation queries
  /*
  const [
    totalProducts,
    activeProducts,
    digitalProducts,
    totalPackages,
    totalOrders,
    popularCategories
  ] = await Promise.all([
    supabase.from('products').select('id', { count: 'exact' }),
    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),
    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),
    supabase.from('product_packages').select('id', { count: 'exact' }),
    supabase.from('orders').select('id', { count: 'exact' }),
    supabase.from('products').select('category').groupBy('category')
  ])
  */
  
  // Temporary: Calculate from localStorage
  const products = await getProducts()

  // Ensure products is an array and has valid structure
  const validProducts = Array.isArray(products) ? products.filter(p => p && typeof p === 'object') : []

  return {
    totalProducts: validProducts.length,
    activeProducts: validProducts.filter(p => p.isActive === true).length,
    digitalProducts: validProducts.filter(p => p.productType === 'digital').length,
    physicalProducts: validProducts.filter(p => p.productType === 'physical').length,
    totalPackages: validProducts.reduce((sum, p) => {
      const packages = p.packages || []
      return sum + (Array.isArray(packages) ? packages.length : 0)
    }, 0),
    totalOrders: 0, // ## TODO: Get from orders table
    popularCategories: getPopularCategories(validProducts)
  }
}

// =====================================================
// HELPER FUNCTIONS
// =====================================================

/**
 * Apply filters to products array (temporary implementation)
 */
function applyFilters(products: ProductTemplate[], filters?: ProductFilters): ProductTemplate[] {
  // Ensure products is a valid array
  const validProducts = Array.isArray(products) ? products.filter(p => p && typeof p === 'object') : []

  if (!filters) return validProducts

  return validProducts.filter(product => {
    // Ensure product has required properties
    if (!product.name || !product.category) return false

    if (filters.category && product.category !== filters.category) return false
    if (filters.productType && product.productType !== filters.productType) return false
    if (filters.processingType && product.processingType !== filters.processingType) return false
    if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false
    if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      const nameMatch = product.name && product.name.toLowerCase().includes(searchLower)
      const descMatch = product.description && product.description.toLowerCase().includes(searchLower)
      if (!nameMatch && !descMatch) return false
    }
    return true
  })
}

/**
 * Get popular categories from products
 */
function getPopularCategories(products: ProductTemplate[]) {
  const categoryCount: Record<string, number> = {}

  // Ensure products is an array and filter valid products
  const validProducts = Array.isArray(products) ? products.filter(p => p && p.category) : []

  validProducts.forEach(product => {
    if (product.category && typeof product.category === 'string') {
      categoryCount[product.category] = (categoryCount[product.category] || 0) + 1
    }
  })

  return Object.entries(categoryCount)
    .map(([category, count]) => ({ category, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
}

/**
 * Generate unique ID (temporary implementation)
 */
function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

// =====================================================
// DATA TRANSFORMATION HELPERS
// =====================================================

/**
 * ## TODO: Transform database product to ProductTemplate interface
 */
function transformProductFromDB(dbProduct: any): ProductTemplate {
  // ## TODO: Implement transformation from Supabase row to ProductTemplate
  return dbProduct
}

/**
 * ## TODO: Transform database package to ProductPackage interface
 */
function transformPackageFromDB(dbPackage: any): ProductPackage {
  // ## TODO: Implement transformation from Supabase row to ProductPackage
  return dbPackage
}
