"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ShoppingCart, Zap, Calculator } from "lucide-react"
import { ProductTemplate, ProductFormData, ProductPackage, Currency } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { useCurrencyConverter } from "@/contexts/CurrencyContext"
import { PackageSelector } from "./PackageSelector"
import { CustomField, validateFormFields } from "../admin/CustomField"

interface InteractiveProductFormProps {
  template: ProductTemplate
  onSubmit: (formData: ProductFormData) => void
  currency: Currency
  showPricing?: boolean
  disabled?: boolean
  className?: string
}

export function InteractiveProductForm({
  template,
  onSubmit,
  currency,
  showPricing = true,
  disabled = false,
  className = ""
}: InteractiveProductFormProps) {
  // ## TODO: Add user authentication check
  // ## TODO: Implement real-time price updates with Supabase
  
  const [selectedPackage, setSelectedPackage] = useState<ProductPackage | null>(null)
  const [quantity, setQuantity] = useState(1)
  const [customFieldValues, setCustomFieldValues] = useState<Record<string, any>>({})
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(false)
  
  const { convertPrice } = useCurrencyConverter()

  // Initialize quantity field if exists
  useEffect(() => {
    const quantityField = template.fields.find(f => f.type === "quantity")
    if (quantityField) {
      setCustomFieldValues(prev => ({
        ...prev,
        [quantityField.name]: 1
      }))
    }
  }, [template.fields])

  /**
   * Convert price from USD to selected currency
   */
  const getConvertedPrice = (priceUSD: number): number => {
    // ## TODO: Replace with real currency conversion from Supabase
    return convertPrice(priceUSD, "USD", currency)
  }

  /**
   * Format price in selected currency
   */
  const formatPrice = (priceUSD: number): string => {
    const convertedPrice = getConvertedPrice(priceUSD)
    return formatCurrency(convertedPrice, currency)
  }

  /**
   * Calculate total price including quantity and modifiers
   */
  const calculateTotalPrice = (): number => {
    if (!selectedPackage) return 0
    
    let basePrice = selectedPackage.price
    let currentQuantity = quantity
    
    // Get quantity from custom fields if quantity field exists
    const quantityField = template.fields.find(f => f.type === "quantity")
    if (quantityField && customFieldValues[quantityField.name]) {
      currentQuantity = parseInt(customFieldValues[quantityField.name]) || 1
    }
    
    // ## TODO: Apply price modifiers from custom fields
    // For now, simple multiplication
    return basePrice * currentQuantity
  }

  /**
   * Handle package selection
   */
  const handlePackageSelect = (pkg: ProductPackage) => {
    setSelectedPackage(pkg)
    
    // Update package field if exists
    const packageField = template.fields.find(f => f.type === "package")
    if (packageField) {
      setCustomFieldValues(prev => ({
        ...prev,
        [packageField.name]: pkg.id
      }))
    }
    
    // Clear field errors
    setFieldErrors({})
  }

  /**
   * Handle custom field value change
   */
  const handleFieldChange = (fieldName: string, value: any) => {
    setCustomFieldValues(prev => ({
      ...prev,
      [fieldName]: value
    }))
    
    // Clear error for this field
    if (fieldErrors[fieldName]) {
      setFieldErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[fieldName]
        return newErrors
      })
    }
    
    // Update quantity if it's a quantity field
    const field = template.fields.find(f => f.name === fieldName)
    if (field?.type === "quantity") {
      setQuantity(parseInt(value) || 1)
    }
  }

  /**
   * Validate form and submit
   */
  const handleSubmit = async () => {
    if (!selectedPackage) {
      alert("يرجى اختيار حزمة")
      return
    }
    
    // Validate all custom fields
    const activeFields = template.fields.filter(f => f.isActive)
    const errors = validateFormFields(activeFields, customFieldValues)
    
    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors)
      return
    }
    
    try {
      setIsLoading(true)
      
      const formData: ProductFormData = {
        templateId: template.id,
        selectedPackage,
        quantity,
        customFields: customFieldValues,
        totalPrice: calculateTotalPrice(),
        currency
      }
      
      await onSubmit(formData)
    } catch (error) {
      console.error("Error submitting form:", error)
      alert("حدث خطأ أثناء إرسال الطلب")
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Get visible fields (excluding package and quantity fields that are handled separately)
   */
  const getVisibleFields = () => {
    return template.fields.filter(field => 
      field.isActive && 
      field.type !== "package" && 
      field.type !== "quantity"
    )
  }

  const totalPrice = calculateTotalPrice()
  const visibleFields = getVisibleFields()

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Package Selection */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Zap className="h-5 w-5" />
            اختر الحزمة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <PackageSelector
            packages={template.packages}
            selectedPackage={selectedPackage}
            onPackageSelect={handlePackageSelect}
            currency={currency}
            showPricing={showPricing}
            disabled={disabled}
          />
        </CardContent>
      </Card>

      {/* Custom Fields */}
      {visibleFields.length > 0 && selectedPackage && (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white">معلومات إضافية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {visibleFields.map((field) => (
              <CustomField
                key={field.id}
                field={field}
                value={customFieldValues[field.name]}
                onChange={(value) => handleFieldChange(field.name, value)}
                error={fieldErrors[field.name]}
                disabled={disabled}
                packages={field.type === "package" ? template.packages : undefined}
                onPackageSelect={field.type === "package" ? handlePackageSelect : undefined}
              />
            ))}
          </CardContent>
        </Card>
      )}

      {/* Quantity Selector (if quantity field exists) */}
      {template.fields.some(f => f.type === "quantity") && selectedPackage && (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white">الكمية</CardTitle>
          </CardHeader>
          <CardContent>
            {template.fields
              .filter(f => f.type === "quantity" && f.isActive)
              .map((field) => (
                <CustomField
                  key={field.id}
                  field={field}
                  value={customFieldValues[field.name] || 1}
                  onChange={(value) => handleFieldChange(field.name, value)}
                  error={fieldErrors[field.name]}
                  disabled={disabled}
                />
              ))}
          </CardContent>
        </Card>
      )}

      {/* Price Summary */}
      {selectedPackage && showPricing && (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              ملخص السعر
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-slate-300">الحزمة المختارة:</span>
              <span className="text-white">{selectedPackage.nameArabic}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-slate-300">سعر الوحدة:</span>
              <span className="text-white">{formatPrice(selectedPackage.price)}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-slate-300">الكمية:</span>
              <span className="text-white">{quantity}</span>
            </div>
            
            {selectedPackage.originalPrice && selectedPackage.originalPrice > selectedPackage.price && (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">السعر الأصلي:</span>
                  <span className="text-slate-500 line-through">
                    {formatPrice(selectedPackage.originalPrice * quantity)}
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-green-400">الخصم:</span>
                  <span className="text-green-400">
                    -{formatPrice((selectedPackage.originalPrice - selectedPackage.price) * quantity)}
                  </span>
                </div>
              </>
            )}
            
            <Separator className="bg-slate-600" />
            
            <div className="flex justify-between items-center text-lg font-bold">
              <span className="text-white">المجموع:</span>
              <span className="text-yellow-400">{formatPrice(totalPrice)}</span>
            </div>
            
            {currency !== "USD" && (
              <p className="text-xs text-slate-500 text-center">
                السعر محول من الدولار الأمريكي • ## TODO: أسعار الصرف من Supabase
              </p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Submit Button */}
      {selectedPackage && (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardContent className="p-6">
            <Button
              onClick={handleSubmit}
              disabled={disabled || isLoading || !selectedPackage}
              className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold py-3 text-lg"
              size="lg"
            >
              {isLoading ? (
                "جاري المعالجة..."
              ) : (
                <>
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  {template.processingType === "instant" ? "اشتري الآن" : "أضف للسلة"}
                  {showPricing && ` - ${formatPrice(totalPrice)}`}
                </>
              )}
            </Button>
            
            {/* Processing Type Badge */}
            <div className="flex justify-center mt-3">
              <Badge 
                className={`${
                  template.processingType === "instant"
                    ? 'bg-green-500/20 text-green-400 border-green-500/30'
                    : 'bg-blue-500/20 text-blue-400 border-blue-500/30'
                }`}
              >
                {template.processingType === "instant" ? (
                  <>
                    <Zap className="h-3 w-3 mr-1" />
                    تسليم فوري
                  </>
                ) : (
                  <>
                    معالجة يدوية - {template.estimatedTime}
                  </>
                )}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Product Features */}
      {template.features && template.features.length > 0 && (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white">مميزات المنتج</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {template.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2 text-slate-300">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
