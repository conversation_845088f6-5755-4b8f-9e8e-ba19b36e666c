"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Plus,
  Upload,
  Download,
  Eye,
  EyeOff,
  Trash2,
  Key,
  Shield,
  Copy,
  RefreshCw,
  AlertTriangle,
  Package,
  BarChart3,
  FileText
} from "lucide-react"
import { DigitalCode, CodeStatus } from "@/lib/types"
import { 
  encryptCode, 
  decryptCode, 
  maskCode, 
  validateCodeFormat,
  generateRandomCodes,
  formatCodeForDisplay,
  cleanCode
} from "@/lib/utils/encryption"

interface CodeInventoryManagerProps {
  productId: string
  productName: string
  onInventoryUpdate: (codes: DigitalCode[]) => void
}

export function CodeInventoryManager({ 
  productId, 
  productName, 
  onInventoryUpdate 
}: CodeInventoryManagerProps) {
  const [codes, setCodes] = useState<DigitalCode[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [bulkCodes, setBulkCodes] = useState("")
  const [showCodes, setShowCodes] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState<CodeStatus | "all">("all")
  const [searchTerm, setSearchTerm] = useState("")

  // Load codes from localStorage on mount
  useEffect(() => {
    loadCodes()
  }, [productId])

  const loadCodes = () => {
    const storageKey = `digital_codes_${productId}`
    const storedCodes = JSON.parse(localStorage.getItem(storageKey) || '[]')
    const loadedCodes = storedCodes.map((code: any) => ({
      ...code,
      createdAt: new Date(code.createdAt),
      assignedAt: code.assignedAt ? new Date(code.assignedAt) : undefined,
      revealedAt: code.revealedAt ? new Date(code.revealedAt) : undefined,
      expiresAt: code.expiresAt ? new Date(code.expiresAt) : undefined
    }))
    setCodes(loadedCodes)
  }

  const saveCodes = (newCodes: DigitalCode[]) => {
    const storageKey = `digital_codes_${productId}`
    localStorage.setItem(storageKey, JSON.stringify(newCodes))
    setCodes(newCodes)
    onInventoryUpdate(newCodes)
  }

  // Generate unique ID
  const generateId = () => `code_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  // Add single code
  const handleAddSingleCode = () => {
    const code = prompt("أدخل الكود الرقمي:")
    if (!code) return

    const cleanedCode = cleanCode(code)
    if (!validateCodeFormat(cleanedCode)) {
      alert("تنسيق الكود غير صحيح")
      return
    }

    try {
      const encryptedCode = encryptCode(cleanedCode)
      const newCode: DigitalCode = {
        id: generateId(),
        productId,
        code: encryptedCode,
        status: "available",
        createdAt: new Date()
      }

      saveCodes([...codes, newCode])
      alert("تم إضافة الكود بنجاح")
    } catch (error) {
      alert("فشل في تشفير الكود")
    }
  }

  // Bulk add codes
  const handleBulkAdd = () => {
    if (!bulkCodes.trim()) {
      alert("يرجى إدخال الأكواد")
      return
    }

    setIsLoading(true)
    try {
      const codeLines = bulkCodes.split('\n').filter(line => line.trim())
      const validCodes: DigitalCode[] = []
      const invalidCodes: string[] = []

      codeLines.forEach(line => {
        const cleanedCode = cleanCode(line.trim())
        if (validateCodeFormat(cleanedCode)) {
          try {
            const encryptedCode = encryptCode(cleanedCode)
            validCodes.push({
              id: generateId(),
              productId,
              code: encryptedCode,
              status: "available",
              createdAt: new Date(),
              batchId: `batch_${Date.now()}`
            })
          } catch {
            invalidCodes.push(line)
          }
        } else {
          invalidCodes.push(line)
        }
      })

      if (validCodes.length > 0) {
        saveCodes([...codes, ...validCodes])
        setBulkCodes("")
        setIsDialogOpen(false)
        alert(`تم إضافة ${validCodes.length} كود بنجاح`)
      }

      if (invalidCodes.length > 0) {
        alert(`فشل في إضافة ${invalidCodes.length} كود بسبب تنسيق غير صحيح`)
      }
    } catch (error) {
      alert("حدث خطأ أثناء إضافة الأكواد")
    } finally {
      setIsLoading(false)
    }
  }

  // Generate random codes
  const handleGenerateRandomCodes = () => {
    const count = parseInt(prompt("كم عدد الأكواد المطلوب توليدها؟") || "0")
    if (count <= 0 || count > 100) {
      alert("يرجى إدخال عدد صحيح بين 1 و 100")
      return
    }

    setIsLoading(true)
    try {
      const randomCodes = generateRandomCodes(count, 12)
      const newCodes: DigitalCode[] = randomCodes.map(code => ({
        id: generateId(),
        productId,
        code: encryptCode(code),
        status: "available",
        createdAt: new Date(),
        batchId: `generated_${Date.now()}`
      }))

      saveCodes([...codes, ...newCodes])
      alert(`تم توليد ${count} كود بنجاح`)
    } catch (error) {
      alert("فشل في توليد الأكواد")
    } finally {
      setIsLoading(false)
    }
  }

  // Delete code
  const handleDeleteCode = (codeId: string) => {
    const updatedCodes = codes.filter(code => code.id !== codeId)
    saveCodes(updatedCodes)
  }

  // Copy code
  const handleCopyCode = async (code: DigitalCode) => {
    try {
      const decryptedCode = decryptCode(code.code)
      await navigator.clipboard.writeText(decryptedCode)
      alert("تم نسخ الكود")
    } catch (error) {
      alert("فشل في نسخ الكود")
    }
  }

  // Get code display
  const getCodeDisplay = (code: DigitalCode) => {
    if (showCodes) {
      try {
        const decryptedCode = decryptCode(code.code)
        return formatCodeForDisplay(decryptedCode)
      } catch {
        return "خطأ في فك التشفير"
      }
    }
    return maskCode("XXXXXXXXXXXX")
  }

  // Filter codes
  const filteredCodes = codes.filter(code => {
    const matchesStatus = selectedStatus === "all" || code.status === selectedStatus
    const matchesSearch = searchTerm === "" || 
      code.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (code.orderId && code.orderId.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (code.userId && code.userId.toLowerCase().includes(searchTerm.toLowerCase()))
    
    return matchesStatus && matchesSearch
  })

  // Statistics
  const stats = {
    total: codes.length,
    available: codes.filter(c => c.status === "available").length,
    reserved: codes.filter(c => c.status === "reserved").length,
    sold: codes.filter(c => c.status === "sold").length,
    expired: codes.filter(c => c.status === "expired").length,
    invalid: codes.filter(c => c.status === "invalid").length
  }

  // Status colors
  const getStatusColor = (status: CodeStatus) => {
    switch (status) {
      case "available": return "bg-green-500/20 text-green-400"
      case "reserved": return "bg-yellow-500/20 text-yellow-400"
      case "sold": return "bg-blue-500/20 text-blue-400"
      case "expired": return "bg-red-500/20 text-red-400"
      case "invalid": return "bg-gray-500/20 text-gray-400"
      default: return "bg-slate-500/20 text-slate-400"
    }
  }

  const getStatusText = (status: CodeStatus) => {
    switch (status) {
      case "available": return "متاح"
      case "reserved": return "محجوز"
      case "sold": return "مباع"
      case "expired": return "منتهي الصلاحية"
      case "invalid": return "غير صالح"
      default: return "غير معروف"
    }
  }

  return (
    <Card className="bg-slate-700/50 border-slate-600">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-white flex items-center gap-2">
            <Package className="h-5 w-5" />
            مخزون الأكواد - {productName}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowCodes(!showCodes)}
              className="border-slate-600 text-slate-300"
            >
              {showCodes ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showCodes ? "إخفاء" : "إظهار"}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Statistics Dashboard */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="bg-slate-600/50 rounded-lg p-3 text-center">
            <div className="text-white font-bold text-lg">{stats.total}</div>
            <div className="text-slate-300 text-sm">إجمالي</div>
          </div>
          <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-3 text-center">
            <div className="text-green-400 font-bold text-lg">{stats.available}</div>
            <div className="text-green-300 text-sm">متاح</div>
          </div>
          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-3 text-center">
            <div className="text-yellow-400 font-bold text-lg">{stats.reserved}</div>
            <div className="text-yellow-300 text-sm">محجوز</div>
          </div>
          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-3 text-center">
            <div className="text-blue-400 font-bold text-lg">{stats.sold}</div>
            <div className="text-blue-300 text-sm">مباع</div>
          </div>
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-center">
            <div className="text-red-400 font-bold text-lg">{stats.expired + stats.invalid}</div>
            <div className="text-red-300 text-sm">غير صالح</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button onClick={handleAddSingleCode} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة كود واحد
          </Button>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="border-slate-600 text-slate-300">
                <Upload className="h-4 w-4 mr-2" />
                إضافة متعددة
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة أكواد متعددة</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>الأكواد (كود واحد في كل سطر)</Label>
                  <Textarea
                    value={bulkCodes}
                    onChange={(e) => setBulkCodes(e.target.value)}
                    placeholder="ABC123DEF456&#10;GHI789JKL012&#10;MNO345PQR678"
                    className="bg-slate-700 border-slate-600 text-white min-h-[120px]"
                    rows={6}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleBulkAdd} disabled={isLoading} className="flex-1">
                    {isLoading ? "جاري الإضافة..." : "إضافة الأكواد"}
                  </Button>
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    إلغاء
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Button 
            variant="outline" 
            onClick={handleGenerateRandomCodes}
            className="border-slate-600 text-slate-300"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            توليد عشوائي
          </Button>
        </div>

        {/* Filters */}
        <div className="flex gap-4">
          <div className="flex-1">
            <Input
              placeholder="بحث بالمعرف أو رقم الطلب..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="bg-slate-600 border-slate-500 text-white"
            />
          </div>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value as CodeStatus | "all")}
            className="bg-slate-600 border-slate-500 text-white rounded px-3 py-2"
          >
            <option value="all">جميع الحالات</option>
            <option value="available">متاح</option>
            <option value="reserved">محجوز</option>
            <option value="sold">مباع</option>
            <option value="expired">منتهي الصلاحية</option>
            <option value="invalid">غير صالح</option>
          </select>
        </div>

        {/* Codes List */}
        {filteredCodes.length === 0 ? (
          <div className="text-center py-8">
            <Key className="h-16 w-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">
              {codes.length === 0 ? "لا توجد أكواد" : "لا توجد نتائج"}
            </h3>
            <p className="text-slate-400 mb-4">
              {codes.length === 0 
                ? "ابدأ بإضافة أكواد رقمية لهذا المنتج"
                : "جرب تغيير المرشحات أو البحث"
              }
            </p>
          </div>
        ) : (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredCodes.map((code) => (
              <div
                key={code.id}
                className="flex items-center justify-between p-3 rounded-lg border border-slate-600 bg-slate-800/30"
              >
                <div className="flex items-center gap-3">
                  <Shield className="h-4 w-4 text-slate-400" />
                  <div>
                    <div className="text-white font-mono text-sm">
                      {getCodeDisplay(code)}
                    </div>
                    <div className="text-slate-400 text-xs">
                      {code.createdAt.toLocaleDateString('ar')}
                      {code.assignedAt && ` • مخصص: ${code.assignedAt.toLocaleDateString('ar')}`}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(code.status)}>
                    {getStatusText(code.status)}
                  </Badge>

                  {code.status === "available" && (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleCopyCode(code)}
                        className="border-slate-600 text-slate-300 hover:bg-slate-700"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-red-600 text-red-400 hover:bg-red-600/10"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-slate-800 border-slate-700">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="text-white">حذف الكود</AlertDialogTitle>
                            <AlertDialogDescription className="text-slate-400">
                              هل أنت متأكد من حذف هذا الكود؟ لا يمكن التراجع عن هذا الإجراء.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="bg-slate-700 text-white border-slate-600">
                              إلغاء
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteCode(code.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              حذف
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Low Stock Warning */}
        {stats.available < 5 && stats.available > 0 && (
          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-3 flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-400" />
            <div className="text-yellow-300 text-sm">
              تحذير: عدد الأكواد المتاحة قليل ({stats.available} أكواد متبقية)
            </div>
          </div>
        )}

        {/* Out of Stock Warning */}
        {stats.available === 0 && stats.total > 0 && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="text-red-300 text-sm">
              تحذير: نفد المخزون! لا توجد أكواد متاحة للبيع
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
