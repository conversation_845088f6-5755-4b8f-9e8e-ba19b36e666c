"use client"

import React, { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Upload,
  Image as ImageIcon,
  X,
  Link,
  Smile
} from "lucide-react"

interface ImageUploaderProps {
  value: string
  onChange: (value: string) => void
  label?: string
  placeholder?: string
  className?: string
}

export function ImageUploader({ 
  value, 
  onChange, 
  label = "الصورة", 
  placeholder = "اختر صورة...",
  className = ""
}: ImageUploaderProps) {
  const [activeTab, setActiveTab] = useState<"upload" | "url" | "emoji">("emoji")
  const [imageUrl, setImageUrl] = useState("")
  const [selectedEmoji, setSelectedEmoji] = useState("")
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Popular gaming emojis
  const gamingEmojis = [
    "🎮", "🕹️", "🎯", "🎲", "🃏", "🎪", "🎨", "🎭", "🎪", "🎨",
    "🔥", "⚡", "💎", "💰", "🏆", "🎁", "🎊", "🎉", "✨", "⭐",
    "🚀", "💫", "🌟", "🔮", "💜", "💙", "💚", "❤️", "🧡", "💛",
    "📱", "💻", "🖥️", "⌨️", "🖱️", "🎧", "🔊", "📺", "📡", "🛡️"
  ]

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت")
        return
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        alert("يرجى اختيار ملف صورة صالح")
        return
      }

      // Convert to base64 for localStorage
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        onChange(result)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleUrlSubmit = () => {
    if (!imageUrl.trim()) {
      alert("يرجى إدخال رابط الصورة")
      return
    }

    // Basic URL validation
    try {
      new URL(imageUrl)
      onChange(imageUrl.trim())
      setImageUrl("")
    } catch {
      alert("رابط غير صالح")
    }
  }

  const handleEmojiSelect = (emoji: string) => {
    setSelectedEmoji(emoji)
    onChange(emoji)
  }

  const clearImage = () => {
    onChange("")
    setImageUrl("")
    setSelectedEmoji("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className={className}>
      <Label className="text-slate-300 mb-2 block">{label} *</Label>
      
      {/* Current Image Preview */}
      {value && (
        <div className="mb-4 p-3 bg-slate-700/50 rounded-lg border border-slate-600">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center overflow-hidden">
                {value.startsWith('data:') || value.startsWith('http') ? (
                  <img 
                    src={value} 
                    alt="Preview" 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-2xl">{value}</span>
                )}
              </div>
              <div>
                <p className="text-white text-sm font-medium">الصورة الحالية</p>
                <p className="text-slate-400 text-xs">
                  {value.startsWith('data:') ? "صورة مرفوعة" : 
                   value.startsWith('http') ? "رابط خارجي" : "رمز تعبيري"}
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={clearImage}
              className="border-red-600 text-red-400 hover:bg-red-600/10"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Image Selection Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-slate-700/50">
          <TabsTrigger value="emoji" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
            <Smile className="h-4 w-4 mr-1" />
            رموز
          </TabsTrigger>
          <TabsTrigger value="upload" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
            <Upload className="h-4 w-4 mr-1" />
            رفع
          </TabsTrigger>
          <TabsTrigger value="url" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
            <Link className="h-4 w-4 mr-1" />
            رابط
          </TabsTrigger>
        </TabsList>

        {/* Emoji Selection */}
        <TabsContent value="emoji" className="space-y-3">
          <p className="text-slate-400 text-sm">اختر رمز تعبيري للفئة</p>
          <div className="grid grid-cols-8 sm:grid-cols-10 gap-2 max-h-40 overflow-y-auto p-2 bg-slate-700/30 rounded-lg">
            {gamingEmojis.map((emoji, index) => (
              <button
                key={index}
                onClick={() => handleEmojiSelect(emoji)}
                className={`w-8 h-8 text-xl hover:bg-slate-600 rounded transition-colors ${
                  selectedEmoji === emoji ? "bg-blue-600" : ""
                }`}
              >
                {emoji}
              </button>
            ))}
          </div>
        </TabsContent>

        {/* File Upload */}
        <TabsContent value="upload" className="space-y-3">
          <p className="text-slate-400 text-sm">ارفع صورة من جهازك (أقل من 5 ميجابايت)</p>
          <div className="flex items-center gap-2">
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="bg-slate-600 border-slate-500 text-white file:bg-slate-700 file:text-white file:border-0 file:mr-2"
            />
          </div>
        </TabsContent>

        {/* URL Input */}
        <TabsContent value="url" className="space-y-3">
          <p className="text-slate-400 text-sm">أدخل رابط صورة من الإنترنت</p>
          <div className="flex gap-2">
            <Input
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="https://example.com/image.jpg"
              className="bg-slate-600 border-slate-500 text-white"
            />
            <Button onClick={handleUrlSubmit} className="bg-blue-600 hover:bg-blue-700">
              إضافة
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
