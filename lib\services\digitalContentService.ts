// =====================================================
// DIGITAL CONTENT SERVICE
// =====================================================
// ## TODO: Implement Supabase integration for digital code management
// ## DATABASE LATER: Connect to encrypted_codes table

import { DigitalContent, DigitalContentDelivery } from '@/lib/types'

// =====================================================
// DIGITAL CODE MANAGEMENT
// =====================================================

/**
 * ## TODO: Implement Supabase code encryption/storage
 * Add digital codes to a package
 */
export async function addDigitalCodes(
  packageId: string, 
  codes: string[], 
  codeType: DigitalContent['type'],
  instructions?: string,
  expiryDays?: number
): Promise<void> {
  // ## TODO: Replace with Supabase encrypted storage
  /*
  const encryptedCodes = codes.map(code => ({
    package_id: packageId,
    code_encrypted: encryptCode(code), // ## TODO: Implement encryption
    code_type: codeType,
    instructions,
    expires_at: expiryDays ? new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000) : null,
    is_used: false,
    is_reserved: false
  }))
  
  const { error } = await supabase
    .from('encrypted_codes')
    .insert(encryptedCodes)
  
  if (error) throw error
  */
  
  // Temporary: Store in localStorage with package reference
  const storageKey = `digital_codes_${packageId}`
  const existingCodes = JSON.parse(localStorage.getItem(storageKey) || '[]')
  
  const newCodes = codes.map(code => ({
    id: generateId(),
    packageId,
    code: code, // ## TODO: Encrypt in production
    type: codeType,
    instructions,
    expiryDate: expiryDays ? new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000) : null,
    isUsed: false,
    isReserved: false,
    createdAt: new Date()
  }))
  
  localStorage.setItem(storageKey, JSON.stringify([...existingCodes, ...newCodes]))
}

/**
 * ## TODO: Implement Supabase code reservation
 * Reserve codes for an order
 */
export async function reserveCodesForOrder(
  packageId: string, 
  quantity: number, 
  orderId: string
): Promise<string[]> {
  // ## TODO: Replace with Supabase transaction
  /*
  const { data, error } = await supabase
    .from('encrypted_codes')
    .update({ 
      is_reserved: true, 
      order_id: orderId,
      reserved_at: new Date().toISOString()
    })
    .eq('package_id', packageId)
    .eq('is_used', false)
    .eq('is_reserved', false)
    .limit(quantity)
    .select('id')
  
  if (error) throw error
  if (data.length < quantity) {
    throw new Error(`Not enough codes available. Requested: ${quantity}, Available: ${data.length}`)
  }
  
  return data.map(row => row.id)
  */
  
  // Temporary: Reserve from localStorage
  const storageKey = `digital_codes_${packageId}`
  const codes = JSON.parse(localStorage.getItem(storageKey) || '[]')
  
  const availableCodes = codes.filter((code: any) => !code.isUsed && !code.isReserved)
  if (availableCodes.length < quantity) {
    throw new Error(`Not enough codes available. Requested: ${quantity}, Available: ${availableCodes.length}`)
  }
  
  const reservedCodes = availableCodes.slice(0, quantity)
  reservedCodes.forEach((code: any) => {
    code.isReserved = true
    code.orderId = orderId
    code.reservedAt = new Date()
  })
  
  localStorage.setItem(storageKey, JSON.stringify(codes))
  return reservedCodes.map((code: any) => code.id)
}

/**
 * ## TODO: Implement Supabase code delivery
 * Deliver reserved codes to customer
 */
export async function deliverCodesToCustomer(orderId: string): Promise<DigitalContent[]> {
  // ## TODO: Replace with Supabase query and decryption
  /*
  const { data, error } = await supabase
    .from('encrypted_codes')
    .update({ 
      is_used: true,
      delivered_at: new Date().toISOString()
    })
    .eq('order_id', orderId)
    .eq('is_reserved', true)
    .select('*')
  
  if (error) throw error
  
  return data.map(row => ({
    id: row.id,
    type: row.code_type,
    title: `${row.code_type} Code`,
    content: decryptCode(row.code_encrypted), // ## TODO: Implement decryption
    instructions: row.instructions,
    expiryDate: row.expires_at ? new Date(row.expires_at) : undefined,
    isRevealed: false,
    deliveredAt: new Date(row.delivered_at),
    accessedAt: undefined
  }))
  */
  
  // Temporary: Find and deliver codes from localStorage
  const allCodes: any[] = []
  
  // Search through all package codes
  const keys = Object.keys(localStorage).filter(key => key.startsWith('digital_codes_'))
  keys.forEach(key => {
    const codes = JSON.parse(localStorage.getItem(key) || '[]')
    allCodes.push(...codes)
  })
  
  const orderCodes = allCodes.filter(code => code.orderId === orderId && code.isReserved)
  
  // Mark as delivered
  orderCodes.forEach(code => {
    code.isUsed = true
    code.deliveredAt = new Date()
  })
  
  // Update localStorage
  keys.forEach(key => {
    const codes = JSON.parse(localStorage.getItem(key) || '[]')
    localStorage.setItem(key, JSON.stringify(codes))
  })
  
  return orderCodes.map(code => ({
    id: code.id,
    type: code.type,
    title: `${code.type} Code`,
    content: code.code, // ## TODO: Decrypt in production
    instructions: code.instructions,
    expiryDate: code.expiryDate ? new Date(code.expiryDate) : undefined,
    isRevealed: false,
    deliveredAt: new Date(code.deliveredAt),
    accessedAt: undefined
  }))
}

/**
 * ## TODO: Implement Supabase code access tracking
 * Mark code as accessed by customer
 */
export async function markCodeAsAccessed(codeId: string): Promise<void> {
  // ## TODO: Replace with Supabase update
  /*
  const { error } = await supabase
    .from('encrypted_codes')
    .update({ accessed_at: new Date().toISOString() })
    .eq('id', codeId)
  
  if (error) throw error
  */
  
  // Temporary: Update in localStorage
  const keys = Object.keys(localStorage).filter(key => key.startsWith('digital_codes_'))
  keys.forEach(key => {
    const codes = JSON.parse(localStorage.getItem(key) || '[]')
    const code = codes.find((c: any) => c.id === codeId)
    if (code) {
      code.accessedAt = new Date()
      localStorage.setItem(key, JSON.stringify(codes))
    }
  })
}

/**
 * ## TODO: Implement Supabase code availability check
 * Check available codes for a package
 */
export async function getAvailableCodesCount(packageId: string): Promise<number> {
  // ## TODO: Replace with Supabase count query
  /*
  const { count, error } = await supabase
    .from('encrypted_codes')
    .select('id', { count: 'exact' })
    .eq('package_id', packageId)
    .eq('is_used', false)
    .eq('is_reserved', false)
  
  if (error) throw error
  return count || 0
  */
  
  // Temporary: Count from localStorage
  const storageKey = `digital_codes_${packageId}`
  const codes = JSON.parse(localStorage.getItem(storageKey) || '[]')
  return codes.filter((code: any) => !code.isUsed && !code.isReserved).length
}

/**
 * ## TODO: Implement Supabase bulk code management
 * Get all codes for admin management
 */
export async function getPackageCodes(packageId: string): Promise<any[]> {
  // ## TODO: Replace with Supabase admin query
  /*
  const { data, error } = await supabase
    .from('encrypted_codes')
    .select('*')
    .eq('package_id', packageId)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
  */
  
  // Temporary: Get from localStorage
  const storageKey = `digital_codes_${packageId}`
  return JSON.parse(localStorage.getItem(storageKey) || '[]')
}

/**
 * ## TODO: Implement Supabase code deletion
 * Delete unused codes
 */
export async function deleteUnusedCodes(packageId: string, codeIds: string[]): Promise<void> {
  // ## TODO: Replace with Supabase delete
  /*
  const { error } = await supabase
    .from('encrypted_codes')
    .delete()
    .eq('package_id', packageId)
    .in('id', codeIds)
    .eq('is_used', false)
    .eq('is_reserved', false)
  
  if (error) throw error
  */
  
  // Temporary: Remove from localStorage
  const storageKey = `digital_codes_${packageId}`
  const codes = JSON.parse(localStorage.getItem(storageKey) || '[]')
  const filteredCodes = codes.filter((code: any) => 
    !codeIds.includes(code.id) || code.isUsed || code.isReserved
  )
  localStorage.setItem(storageKey, JSON.stringify(filteredCodes))
}

// =====================================================
// DIGITAL CONTENT DELIVERY
// =====================================================

/**
 * ## TODO: Implement complete digital delivery workflow
 * Process digital content delivery for an order
 */
export async function processDigitalDelivery(
  orderId: string,
  packageId: string,
  quantity: number
): Promise<DigitalContentDelivery> {
  try {
    // Reserve codes
    const reservedCodeIds = await reserveCodesForOrder(packageId, quantity, orderId)
    
    // Deliver codes (for instant delivery)
    const deliveredCodes = await deliverCodesToCustomer(orderId)
    
    return {
      status: 'delivered',
      contents: deliveredCodes,
      deliveryMethod: 'instant',
      estimatedDeliveryTime: 'فوري',
      lastUpdated: new Date()
    }
  } catch (error) {
    console.error('Digital delivery failed:', error)
    return {
      status: 'pending',
      contents: [],
      deliveryMethod: 'manual',
      estimatedDeliveryTime: '5-10 دقائق',
      lastUpdated: new Date()
    }
  }
}

// =====================================================
// HELPER FUNCTIONS
// =====================================================

/**
 * Generate unique ID (temporary implementation)
 */
function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * ## TODO: Implement proper encryption
 * Encrypt code for storage
 */
function encryptCode(code: string): string {
  // ## TODO: Implement proper encryption using crypto library
  return btoa(code) // Temporary base64 encoding
}

/**
 * ## TODO: Implement proper decryption
 * Decrypt code for delivery
 */
function decryptCode(encryptedCode: string): string {
  // ## TODO: Implement proper decryption
  return atob(encryptedCode) // Temporary base64 decoding
}
