"use client"

import React from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Plus, Minus, Upload } from "lucide-react"
import { DynamicField, ProductPackage } from "@/lib/types"

interface CustomFieldProps {
  field: DynamicField
  value: any
  onChange: (value: any) => void
  error?: string
  disabled?: boolean
  packages?: ProductPackage[] // For package selector fields
  onPackageSelect?: (pkg: ProductPackage) => void
}

export function CustomField({ 
  field, 
  value, 
  onChange, 
  error, 
  disabled = false,
  packages = [],
  onPackageSelect
}: CustomFieldProps) {
  
  /**
   * Render field based on type
   */
  const renderField = () => {
    switch (field.type) {
      case "text":
        return (
          <Input
            type="text"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={`bg-slate-600 border-slate-500 text-white ${error ? 'border-red-500' : ''}`}
          />
        )

      case "number":
        return (
          <Input
            type="number"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            min={field.validation?.min}
            max={field.validation?.max}
            className={`bg-slate-600 border-slate-500 text-white ${error ? 'border-red-500' : ''}`}
          />
        )

      case "email":
        return (
          <Input
            type="email"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={`bg-slate-600 border-slate-500 text-white ${error ? 'border-red-500' : ''}`}
          />
        )

      case "textarea":
        return (
          <Textarea
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            className={`bg-slate-600 border-slate-500 text-white min-h-[100px] ${error ? 'border-red-500' : ''}`}
          />
        )

      case "dropdown":
        return (
          <Select value={value || ""} onValueChange={onChange} disabled={disabled}>
            <SelectTrigger className={`bg-slate-600 border-slate-500 text-white ${error ? 'border-red-500' : ''}`}>
              <SelectValue placeholder={field.placeholder || "اختر..."} />
            </SelectTrigger>
            <SelectContent className="bg-slate-600 border-slate-500">
              {field.options?.map((option, index) => (
                <SelectItem key={index} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case "package":
        return (
          <div className="space-y-3">
            {packages.length === 0 ? (
              <div className="text-slate-400 text-sm p-4 border border-slate-600 rounded">
                ## TODO: تحميل الحزم من قاعدة البيانات
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {packages.map((pkg) => (
                  <button
                    key={pkg.id}
                    type="button"
                    onClick={() => {
                      onChange(pkg.id)
                      onPackageSelect?.(pkg)
                    }}
                    disabled={disabled}
                    className={`relative p-4 rounded-lg border-2 transition-all duration-300 text-center ${
                      value === pkg.id
                        ? 'border-yellow-400 bg-yellow-400/10'
                        : 'border-slate-600 bg-slate-700/30 hover:border-slate-500'
                    } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {pkg.popular && (
                      <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs px-2 py-1 rounded">
                        الأشهر
                      </div>
                    )}
                    
                    <div className="text-white font-medium text-sm mb-1">
                      {pkg.name}
                    </div>
                    <div className="text-slate-400 text-xs mb-2">
                      {pkg.amount}
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-yellow-400 font-bold text-sm">
                        ${pkg.price}
                      </div>
                      {pkg.originalPrice && pkg.originalPrice > pkg.price && (
                        <div className="flex items-center justify-center gap-1">
                          <span className="text-slate-500 line-through text-xs">
                            ${pkg.originalPrice}
                          </span>
                          {pkg.discount && (
                            <span className="bg-red-500/20 text-red-400 text-xs px-1 rounded">
                              -{pkg.discount}%
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        )

      case "quantity":
        const quantity = parseInt(value) || 1
        return (
          <div className="flex items-center gap-3">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => onChange(Math.max(1, quantity - 1))}
              disabled={disabled || quantity <= 1}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <Minus className="h-4 w-4" />
            </Button>
            
            <div className="flex-1 text-center">
              <Input
                type="number"
                value={quantity}
                onChange={(e) => onChange(Math.max(1, parseInt(e.target.value) || 1))}
                min="1"
                disabled={disabled}
                className="bg-slate-600 border-slate-500 text-white text-center"
              />
            </div>
            
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => onChange(quantity + 1)}
              disabled={disabled}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        )

      case "image":
        return (
          <div className="space-y-3">
            <div className="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center">
              <Upload className="h-8 w-8 text-slate-500 mx-auto mb-2" />
              <p className="text-slate-400 mb-2">اسحب الصورة هنا أو انقر للتحديد</p>
              <Button 
                type="button"
                variant="outline" 
                disabled={disabled}
                className="border-slate-600 text-slate-300"
              >
                <Upload className="h-4 w-4 mr-2" />
                تحديد صورة
              </Button>
              <p className="text-xs text-slate-500 mt-2">
                ## TODO: تنفيذ رفع الصور إلى Supabase Storage
              </p>
            </div>
            {value && (
              <div className="text-sm text-slate-400">
                الملف المحدد: {value}
              </div>
            )}
          </div>
        )

      default:
        return (
          <div className="text-slate-400 text-sm p-4 border border-slate-600 rounded">
            نوع حقل غير مدعوم: {field.type}
          </div>
        )
    }
  }

  /**
   * Validate field value
   */
  const validateField = (value: any): string | null => {
    // Required field validation
    if (field.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return "هذا الحقل مطلوب"
    }

    // Type-specific validation
    if (value && field.validation) {
      const validation = field.validation

      // Text/textarea length validation
      if ((field.type === "text" || field.type === "textarea") && typeof value === 'string') {
        if (validation.minLength && value.length < validation.minLength) {
          return `يجب أن يكون النص ${validation.minLength} أحرف على الأقل`
        }
        if (validation.maxLength && value.length > validation.maxLength) {
          return `يجب أن يكون النص ${validation.maxLength} أحرف كحد أقصى`
        }
        if (validation.pattern) {
          const regex = new RegExp(validation.pattern)
          if (!regex.test(value)) {
            return "تنسيق غير صحيح"
          }
        }
      }

      // Number validation
      if (field.type === "number") {
        const numValue = parseFloat(value)
        if (isNaN(numValue)) {
          return "يجب إدخال رقم صحيح"
        }
        if (validation.min !== undefined && numValue < validation.min) {
          return `يجب أن يكون الرقم ${validation.min} أو أكثر`
        }
        if (validation.max !== undefined && numValue > validation.max) {
          return `يجب أن يكون الرقم ${validation.max} أو أقل`
        }
      }

      // Email validation
      if (field.type === "email" && typeof value === 'string') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
          return "عنوان بريد إلكتروني غير صحيح"
        }
      }
    }

    return null
  }

  // Get current validation error
  const validationError = error || validateField(value)

  return (
    <div className="space-y-2">
      <Label className="text-slate-300 flex items-center gap-1">
        {field.label}
        {field.required && <span className="text-red-400">*</span>}
      </Label>
      
      {renderField()}
      
      {validationError && (
        <p className="text-red-400 text-sm">{validationError}</p>
      )}
      
      {/* Field help text */}
      {field.type === "package" && (
        <p className="text-slate-500 text-xs">
          اختر الحزمة المناسبة لك
        </p>
      )}
      
      {field.type === "quantity" && (
        <p className="text-slate-500 text-xs">
          حدد الكمية المطلوبة
        </p>
      )}
    </div>
  )
}

/**
 * Utility function to validate all fields in a form
 */
export function validateFormFields(
  fields: DynamicField[], 
  formData: Record<string, any>
): Record<string, string> {
  const errors: Record<string, string> = {}
  
  fields.forEach(field => {
    if (!field.isActive) return
    
    const value = formData[field.name]
    
    // Required field validation
    if (field.required && (!value || (typeof value === 'string' && !value.trim()))) {
      errors[field.name] = "هذا الحقل مطلوب"
      return
    }
    
    // Skip other validations if field is empty and not required
    if (!value) return
    
    // Type-specific validation
    if (field.validation) {
      const validation = field.validation
      
      if ((field.type === "text" || field.type === "textarea") && typeof value === 'string') {
        if (validation.minLength && value.length < validation.minLength) {
          errors[field.name] = `يجب أن يكون النص ${validation.minLength} أحرف على الأقل`
        } else if (validation.maxLength && value.length > validation.maxLength) {
          errors[field.name] = `يجب أن يكون النص ${validation.maxLength} أحرف كحد أقصى`
        } else if (validation.pattern) {
          const regex = new RegExp(validation.pattern)
          if (!regex.test(value)) {
            errors[field.name] = "تنسيق غير صحيح"
          }
        }
      }
      
      if (field.type === "number") {
        const numValue = parseFloat(value)
        if (isNaN(numValue)) {
          errors[field.name] = "يجب إدخال رقم صحيح"
        } else if (validation.min !== undefined && numValue < validation.min) {
          errors[field.name] = `يجب أن يكون الرقم ${validation.min} أو أكثر`
        } else if (validation.max !== undefined && numValue > validation.max) {
          errors[field.name] = `يجب أن يكون الرقم ${validation.max} أو أقل`
        }
      }
      
      if (field.type === "email" && typeof value === 'string') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
          errors[field.name] = "عنوان بريد إلكتروني غير صحيح"
        }
      }
    }
  })
  
  return errors
}
