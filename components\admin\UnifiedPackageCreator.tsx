"use client"

import React, { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft, 
  ArrowRight, 
  Save, 
  Plus, 
  Settings,
  Package,
  Zap,
  Key
} from "lucide-react"
import { ProductDeliveryType, DynamicField, UniversalInputValidation } from "@/lib/types"
import { ProductTypeSelector } from "./ProductTypeSelector"
import { GameSelector } from "./GameSelector"
import { UniversalInputField } from "./UniversalInputField"

interface GameInfo {
  id: string
  name: string
  nameEnglish: string
  icon: string
  category: string
  popular: boolean
  trending: boolean
  description: string
  supportedTypes: ("direct_charge" | "code_based")[]
}

interface PackageData {
  // Basic Info
  name: string
  description: string
  price: number
  originalPrice?: number
  
  // Product Configuration
  deliveryType: ProductDeliveryType
  selectedGame?: GameInfo
  
  // Custom Fields
  customFields: DynamicField[]
  
  // Package Options (for games like PUBG UC amounts)
  packages?: Array<{
    id: string
    name: string
    amount: string
    price: number
  }>
}

interface UnifiedPackageCreatorProps {
  onSave: (packageData: PackageData) => void
  onCancel: () => void
  initialData?: Partial<PackageData>
}

export function UnifiedPackageCreator({ 
  onSave, 
  onCancel, 
  initialData 
}: UnifiedPackageCreatorProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [packageData, setPackageData] = useState<PackageData>({
    name: "",
    description: "",
    price: 0,
    deliveryType: "direct_charge",
    customFields: [],
    ...initialData
  })

  const steps = [
    { id: 1, title: "نوع المنتج", description: "اختر طريقة التسليم" },
    { id: 2, title: "اللعبة/الخدمة", description: "حدد اللعبة أو الخدمة" },
    { id: 3, title: "تفاصيل المنتج", description: "الاسم والسعر والوصف" },
    { id: 4, title: "الحقول المخصصة", description: "إعداد حقول الإدخال" },
    { id: 5, title: "المراجعة والحفظ", description: "مراجعة نهائية" }
  ]

  // Generate field ID
  const generateFieldId = () => `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

  // Add new custom field
  const addCustomField = (validationType: UniversalInputValidation = "text") => {
    const newField: DynamicField = {
      id: generateFieldId(),
      type: "universal_input",
      name: `field_${packageData.customFields.length + 1}`,
      label: `حقل ${packageData.customFields.length + 1}`,
      placeholder: "",
      required: false,
      inputValidation: validationType,
      sortOrder: packageData.customFields.length,
      isActive: true
    }

    setPackageData(prev => ({
      ...prev,
      customFields: [...prev.customFields, newField]
    }))
  }

  // Update custom field
  const updateCustomField = (fieldId: string, updatedField: DynamicField) => {
    setPackageData(prev => ({
      ...prev,
      customFields: prev.customFields.map(field => 
        field.id === fieldId ? updatedField : field
      )
    }))
  }

  // Remove custom field
  const removeCustomField = (fieldId: string) => {
    setPackageData(prev => ({
      ...prev,
      customFields: prev.customFields.filter(field => field.id !== fieldId)
    }))
  }

  // Navigation
  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  // Validation
  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return !!packageData.deliveryType
      case 2:
        return !!packageData.selectedGame
      case 3:
        return packageData.name.trim() && packageData.price > 0
      case 4:
        return true // Custom fields are optional
      case 5:
        return true
      default:
        return false
    }
  }

  // Save package
  const handleSave = () => {
    onSave(packageData)
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <ProductTypeSelector
            selectedType={packageData.deliveryType}
            onTypeChange={(type) => setPackageData(prev => ({ ...prev, deliveryType: type }))}
          />
        )

      case 2:
        return (
          <GameSelector
            selectedGame={packageData.selectedGame}
            onGameSelect={(game) => setPackageData(prev => ({ ...prev, selectedGame: game }))}
            productType={packageData.deliveryType}
          />
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-white font-bold text-lg mb-2">تفاصيل المنتج</h3>
              <p className="text-slate-400 text-sm">أدخل اسم المنتج والسعر والوصف</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label className="text-slate-300">اسم المنتج</Label>
                <Input
                  value={packageData.name}
                  onChange={(e) => setPackageData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="مثال: PUBG Mobile 60 UC"
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-slate-300">السعر</Label>
                  <Input
                    type="number"
                    value={packageData.price}
                    onChange={(e) => setPackageData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                    placeholder="0.00"
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
                <div>
                  <Label className="text-slate-300">السعر الأصلي (اختياري)</Label>
                  <Input
                    type="number"
                    value={packageData.originalPrice || ""}
                    onChange={(e) => setPackageData(prev => ({ ...prev, originalPrice: parseFloat(e.target.value) || undefined }))}
                    placeholder="0.00"
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>

              <div>
                <Label className="text-slate-300">الوصف</Label>
                <Textarea
                  value={packageData.description}
                  onChange={(e) => setPackageData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="وصف المنتج..."
                  className="bg-slate-700 border-slate-600 text-white"
                  rows={3}
                />
              </div>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-white font-bold text-lg mb-2">الحقول المخصصة</h3>
              <p className="text-slate-400 text-sm">
                أضف الحقول التي يحتاجها العميل لإتمام الطلب
              </p>
            </div>

            {/* Quick Add Buttons */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => addCustomField("id")}
                className="border-slate-600 text-slate-300"
              >
                + معرف اللاعب
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => addCustomField("email")}
                className="border-slate-600 text-slate-300"
              >
                + بريد إلكتروني
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => addCustomField("phone")}
                className="border-slate-600 text-slate-300"
              >
                + رقم هاتف
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => addCustomField("text")}
                className="border-slate-600 text-slate-300"
              >
                + حقل نص
              </Button>
            </div>

            {/* Custom Fields List */}
            <div className="space-y-4">
              {packageData.customFields.map((field, index) => (
                <Card key={field.id} className="bg-slate-800/50 border-slate-600">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <Badge className="bg-blue-500/20 text-blue-400">
                        حقل {index + 1}
                      </Badge>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => removeCustomField(field.id)}
                        className="border-red-600 text-red-400 hover:bg-red-600/10"
                      >
                        حذف
                      </Button>
                    </div>

                    <UniversalInputField
                      field={field}
                      value=""
                      onChange={() => {}}
                      isEditing={true}
                      onFieldUpdate={(updatedField) => updateCustomField(field.id, updatedField)}
                    />
                  </CardContent>
                </Card>
              ))}

              {packageData.customFields.length === 0 && (
                <div className="text-center py-8">
                  <Settings className="h-16 w-16 text-slate-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">لا توجد حقول مخصصة</h3>
                  <p className="text-slate-400 mb-4">
                    أضف الحقول التي يحتاجها العملاء لإتمام الطلب
                  </p>
                  <Button onClick={() => addCustomField("text")} className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة حقل
                  </Button>
                </div>
              )}
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-white font-bold text-lg mb-2">مراجعة المنتج</h3>
              <p className="text-slate-400 text-sm">تأكد من صحة جميع البيانات قبل الحفظ</p>
            </div>

            <Card className="bg-slate-800/50 border-slate-600">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Product Type */}
                  <div className="flex items-center gap-3">
                    {packageData.deliveryType === "direct_charge" ? (
                      <Zap className="h-5 w-5 text-blue-400" />
                    ) : (
                      <Key className="h-5 w-5 text-purple-400" />
                    )}
                    <div>
                      <p className="text-white font-medium">
                        {packageData.deliveryType === "direct_charge" ? "شحن مباشر" : "أكواد رقمية"}
                      </p>
                      <p className="text-slate-400 text-sm">نوع المنتج</p>
                    </div>
                  </div>

                  {/* Selected Game */}
                  {packageData.selectedGame && (
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{packageData.selectedGame.icon}</div>
                      <div>
                        <p className="text-white font-medium">{packageData.selectedGame.name}</p>
                        <p className="text-slate-400 text-sm">اللعبة المحددة</p>
                      </div>
                    </div>
                  )}

                  {/* Product Details */}
                  <div className="border-t border-slate-600 pt-4">
                    <h4 className="text-white font-medium mb-2">تفاصيل المنتج</h4>
                    <div className="space-y-2">
                      <p className="text-slate-300"><span className="text-slate-400">الاسم:</span> {packageData.name}</p>
                      <p className="text-slate-300"><span className="text-slate-400">السعر:</span> ${packageData.price}</p>
                      {packageData.originalPrice && (
                        <p className="text-slate-300"><span className="text-slate-400">السعر الأصلي:</span> ${packageData.originalPrice}</p>
                      )}
                      {packageData.description && (
                        <p className="text-slate-300"><span className="text-slate-400">الوصف:</span> {packageData.description}</p>
                      )}
                    </div>
                  </div>

                  {/* Custom Fields */}
                  {packageData.customFields.length > 0 && (
                    <div className="border-t border-slate-600 pt-4">
                      <h4 className="text-white font-medium mb-2">الحقول المخصصة ({packageData.customFields.length})</h4>
                      <div className="space-y-2">
                        {packageData.customFields.map((field, index) => (
                          <div key={field.id} className="flex items-center gap-2">
                            <Badge className="bg-slate-600 text-slate-300 text-xs">
                              {field.inputValidation}
                            </Badge>
                            <span className="text-slate-300 text-sm">{field.label}</span>
                            {field.required && <span className="text-red-400 text-xs">*</span>}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900" dir="rtl">
      <div className="p-6">
        {/* Header */}
        <Card className="bg-slate-800/50 border-slate-700/50 mb-6">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Package className="h-5 w-5" />
              إنشاء منتج جديد
            </CardTitle>
          </CardHeader>
        </Card>

        {/* Steps Progress */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.id 
                    ? 'bg-blue-600 border-blue-600 text-white' 
                    : 'border-slate-600 text-slate-400'
                }`}>
                  {step.id}
                </div>
                <div className="mr-3">
                  <p className={`font-medium ${currentStep >= step.id ? 'text-white' : 'text-slate-400'}`}>
                    {step.title}
                  </p>
                  <p className="text-slate-500 text-sm">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <ArrowLeft className="h-4 w-4 text-slate-600 mx-4" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <Card className="bg-slate-800/50 border-slate-700/50 mb-6">
          <CardContent className="p-6">
            {renderStepContent()}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between">
          <div className="flex gap-2">
            {currentStep > 1 && (
              <Button onClick={prevStep} variant="outline" className="border-slate-600 text-slate-300">
                <ArrowRight className="h-4 w-4 mr-2" />
                السابق
              </Button>
            )}
            <Button onClick={onCancel} variant="outline" className="border-slate-600 text-slate-300">
              إلغاء
            </Button>
          </div>

          <div>
            {currentStep < steps.length ? (
              <Button 
                onClick={nextStep} 
                disabled={!canProceed()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                التالي
                <ArrowLeft className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button 
                onClick={handleSave}
                className="bg-green-600 hover:bg-green-700"
              >
                <Save className="h-4 w-4 mr-2" />
                حفظ المنتج
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
