"use client"

import React, { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Plus,
  Edit,
  Trash2,
  Star,
  Package,
  DollarSign,
  Percent
} from "lucide-react"
import { ProductPackage } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"

interface PackageEditorProps {
  packages: ProductPackage[]
  onPackagesUpdate: (packages: ProductPackage[]) => void
}

export function PackageEditor({ packages, onPackagesUpdate }: PackageEditorProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingPackage, setEditingPackage] = useState<ProductPackage | null>(null)
  const [packageForm, setPackageForm] = useState({
    name: "",
    amount: "",
    price: 0,
    originalPrice: 0,
    discount: 0,
    description: "",
    popular: false,
    isActive: true
  })

  /**
   * Reset form to default values
   */
  const resetForm = () => {
    setPackageForm({
      name: "",
      amount: "",
      price: 0,
      originalPrice: 0,
      discount: 0,
      description: "",
      popular: false,
      isActive: true
    })
    setEditingPackage(null)
  }

  /**
   * Open dialog for creating new package
   */
  const handleCreatePackage = () => {
    resetForm()
    setIsDialogOpen(true)
  }

  /**
   * Open dialog for editing existing package
   */
  const handleEditPackage = (pkg: ProductPackage) => {
    setPackageForm({
      name: pkg.name,
      amount: pkg.amount,
      price: pkg.price,
      originalPrice: pkg.originalPrice || 0,
      discount: pkg.discount || 0,
      description: pkg.description || "",
      popular: pkg.popular || false,
      isActive: pkg.isActive
    })
    setEditingPackage(pkg)
    setIsDialogOpen(true)
  }

  /**
   * Save package (create or update)
   */
  const handleSavePackage = () => {
    // Basic validation
    if (!packageForm.name.trim()) {
      alert("يرجى إدخال اسم الحزمة")
      return
    }

    if (packageForm.price <= 0) {
      alert("يرجى إدخال سعر صحيح")
      return
    }

    const newPackage: ProductPackage = {
      id: editingPackage?.id || generateId(),
      name: packageForm.name,
      nameArabic: packageForm.nameArabic,
      amount: packageForm.amount,
      price: packageForm.price,
      originalPrice: packageForm.originalPrice > 0 ? packageForm.originalPrice : undefined,
      discount: packageForm.discount > 0 ? packageForm.discount : undefined,
      description: packageForm.description || undefined,
      popular: packageForm.popular,
      isActive: packageForm.isActive,
      sortOrder: editingPackage?.sortOrder || packages.length,
      digitalCodes: editingPackage?.digitalCodes || [] // ## TODO: Implement digital codes management
    }

    let updatedPackages: ProductPackage[]
    
    if (editingPackage) {
      // Update existing package
      updatedPackages = packages.map(pkg => 
        pkg.id === editingPackage.id ? newPackage : pkg
      )
    } else {
      // Add new package
      updatedPackages = [...packages, newPackage]
    }

    onPackagesUpdate(updatedPackages)
    setIsDialogOpen(false)
    resetForm()
  }

  /**
   * Delete package
   */
  const handleDeletePackage = (packageId: string) => {
    if (!confirm("هل أنت متأكد من حذف هذه الحزمة؟")) return
    
    const updatedPackages = packages.filter(pkg => pkg.id !== packageId)
    onPackagesUpdate(updatedPackages)
  }

  /**
   * Toggle package popular status
   */
  const togglePopular = (packageId: string) => {
    const updatedPackages = packages.map(pkg => ({
      ...pkg,
      popular: pkg.id === packageId ? !pkg.popular : pkg.popular
    }))
    onPackagesUpdate(updatedPackages)
  }

  /**
   * Calculate discount percentage automatically
   */
  const calculateDiscount = (original: number, current: number) => {
    if (original > 0 && current > 0 && original > current) {
      return Math.round(((original - current) / original) * 100)
    }
    return 0
  }

  /**
   * Handle price changes and auto-calculate discount
   */
  const handlePriceChange = (field: 'price' | 'originalPrice', value: number) => {
    const newForm = { ...packageForm, [field]: value }
    
    if (field === 'originalPrice' && value > 0 && newForm.price > 0) {
      newForm.discount = calculateDiscount(value, newForm.price)
    } else if (field === 'price' && newForm.originalPrice > 0) {
      newForm.discount = calculateDiscount(newForm.originalPrice, value)
    }
    
    setPackageForm(newForm)
  }

  /**
   * Generate unique ID
   */
  const generateId = () => {
    return Math.random().toString(36).substr(2, 9)
  }

  return (
    <Card className="bg-slate-700/50 border-slate-600">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-white flex items-center gap-2">
            <Package className="h-5 w-5" />
            حزم المنتج والأسعار
          </CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={handleCreatePackage} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                إضافة حزمة
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingPackage ? "تعديل الحزمة" : "إضافة حزمة جديدة"}
                </DialogTitle>
              </DialogHeader>
              
              <div className="space-y-4">
                {/* Package Name */}
                <div>
                  <Label className="text-slate-300">اسم الحزمة</Label>
                  <Input
                    value={packageForm.name}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="60 يوسي"
                    className="bg-slate-600 border-slate-500 text-white"
                  />
                </div>

                {/* Amount Display */}
                <div>
                  <Label className="text-slate-300">الكمية المعروضة</Label>
                  <Input
                    value={packageForm.amount}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, amount: e.target.value }))}
                    placeholder="60 UC"
                    className="bg-slate-600 border-slate-500 text-white"
                  />
                </div>

                {/* Pricing */}
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label className="text-slate-300">السعر الحالي (USD)</Label>
                    <Input
                      type="number"
                      step="0.01"
                      value={packageForm.price}
                      onChange={(e) => handlePriceChange('price', parseFloat(e.target.value) || 0)}
                      placeholder="25.00"
                      className="bg-slate-600 border-slate-500 text-white"
                    />
                  </div>
                  <div>
                    <Label className="text-slate-300">السعر الأصلي (اختياري)</Label>
                    <Input
                      type="number"
                      step="0.01"
                      value={packageForm.originalPrice}
                      onChange={(e) => handlePriceChange('originalPrice', parseFloat(e.target.value) || 0)}
                      placeholder="30.00"
                      className="bg-slate-600 border-slate-500 text-white"
                    />
                  </div>
                </div>

                {/* Auto-calculated discount */}
                {packageForm.discount > 0 && (
                  <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-3">
                    <div className="flex items-center gap-2 text-green-400">
                      <Percent className="h-4 w-4" />
                      <span>خصم تلقائي: {packageForm.discount}%</span>
                    </div>
                  </div>
                )}

                {/* Description */}
                <div>
                  <Label className="text-slate-300">الوصف (اختياري)</Label>
                  <Input
                    value={packageForm.description}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="وصف الحزمة..."
                    className="bg-slate-600 border-slate-500 text-white"
                  />
                </div>

                {/* Switches */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-slate-300">حزمة شائعة</Label>
                    <Switch
                      checked={packageForm.popular}
                      onCheckedChange={(checked) => setPackageForm(prev => ({ ...prev, popular: checked }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-slate-300">نشطة</Label>
                    <Switch
                      checked={packageForm.isActive}
                      onCheckedChange={(checked) => setPackageForm(prev => ({ ...prev, isActive: checked }))}
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                    className="border-slate-600 text-slate-300"
                  >
                    إلغاء
                  </Button>
                  <Button onClick={handleSavePackage} className="bg-blue-600 hover:bg-blue-700">
                    {editingPackage ? "تحديث" : "إضافة"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent>
        {packages.length === 0 ? (
          <div className="text-center py-8">
            <Package className="h-16 w-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">لا توجد حزم</h3>
            <p className="text-slate-400 mb-4">ابدأ بإضافة حزمة واحدة على الأقل لمنتجك</p>
            <Button onClick={handleCreatePackage} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              إضافة حزمة
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {packages.map((pkg) => (
              <Card key={pkg.id} className="bg-slate-600/50 border-slate-500 relative">
                {pkg.popular && (
                  <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs">
                    الأشهر
                  </Badge>
                )}
                
                <CardContent className="p-4">
                  <div className="text-center mb-3">
                    <h4 className="text-white font-medium">{pkg.name}</h4>
                    <p className="text-slate-400 text-sm">{pkg.amount}</p>
                  </div>
                  
                  <div className="text-center mb-3">
                    <div className="text-yellow-400 font-bold text-lg">
                      {formatCurrency(pkg.price, "USD")}
                    </div>
                    {pkg.originalPrice && pkg.originalPrice > pkg.price && (
                      <div className="flex items-center justify-center gap-2 text-sm">
                        <span className="text-slate-500 line-through">
                          {formatCurrency(pkg.originalPrice, "USD")}
                        </span>
                        {pkg.discount && (
                          <Badge className="bg-red-500/20 text-red-400 text-xs">
                            -{pkg.discount}%
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>

                  {pkg.description && (
                    <p className="text-slate-400 text-xs text-center mb-3">{pkg.description}</p>
                  )}

                  <div className="flex justify-between items-center mb-3">
                    <Badge variant={pkg.isActive ? "default" : "secondary"} className="text-xs">
                      {pkg.isActive ? "نشط" : "غير نشط"}
                    </Badge>
                    <button
                      onClick={() => togglePopular(pkg.id)}
                      className={`p-1 rounded ${pkg.popular ? 'text-yellow-400' : 'text-slate-500'}`}
                    >
                      <Star className="h-4 w-4" />
                    </button>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditPackage(pkg)}
                      className="flex-1 border-slate-500 text-slate-300 hover:bg-slate-700"
                    >
                      <Edit className="h-3 w-3 mr-1" />
                      تعديل
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeletePackage(pkg.id)}
                      className="border-red-600 text-red-400 hover:bg-red-600/10"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>

                  {/* Digital Codes Info */}
                  {pkg.digitalCodes && pkg.digitalCodes.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-slate-500">
                      <div className="text-xs text-slate-400 text-center">
                        {pkg.digitalCodes.length} كود رقمي متاح
                        {/* ## TODO: Link to digital codes management */}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
