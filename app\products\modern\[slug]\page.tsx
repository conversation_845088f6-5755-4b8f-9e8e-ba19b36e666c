"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { ModernProductPage } from "@/components/products/ModernProductPage"
import { ProductTemplate, ProductFormData } from "@/lib/types"
import { generateId } from "@/lib/utils"

// Mock product data that matches MENA gaming store style
const mockProducts: Record<string, ProductTemplate> = {
  "pubg-uc": {
    id: "pubg-uc-001",
    name: "شحن يوسي PUBG Mobile",
    description: "شحن فوري لعملة UC في لعبة PUBG Mobile - أسرع وأأمن طريقة للحصول على اليوسي",
    category: "ألعاب الموبايل",
    productType: "digital",
    processingType: "instant",
    estimatedTime: "فوري",
    packages: [
      {
        id: generateId(),
        name: "60 يوسي",
        amount: "60 UC",
        price: 5,
        originalPrice: 6,
        discount: 17,
        popular: false,
        isActive: true,
        sortOrder: 0,
        digitalCodes: []
      },
      {
        id: generateId(),
        name: "325 يوسي",
        amount: "325 UC",
        price: 25,
        originalPrice: 30,
        discount: 17,
        popular: true,
        isActive: true,
        sortOrder: 1,
        digitalCodes: []
      },
      {
        id: generateId(),
        name: "660 يوسي",
        amount: "660 UC",
        price: 50,
        originalPrice: 60,
        discount: 17,
        popular: false,
        isActive: true,
        sortOrder: 2,
        digitalCodes: []
      },
      {
        id: generateId(),
        name: "1800 يوسي",
        amount: "1800 UC",
        price: 120,
        originalPrice: 150,
        discount: 20,
        popular: false,
        isActive: true,
        sortOrder: 3,
        digitalCodes: []
      }
    ],
    fields: [
      {
        id: generateId(),
        type: "text",
        name: "player_id",
        label: "الايدي الخاص بك",
        placeholder: "أدخل معرف اللاعب...",
        required: true,
        validation: {
          minLength: 8,
          maxLength: 12,
          pattern: "^[0-9]+$"
        },
        sortOrder: 0,
        isActive: true
      },
      {
        id: generateId(),
        type: "dropdown",
        name: "server",
        label: "اختار الطريقة التي تدخل بها حسابك",
        placeholder: "اختر طريقة الدخول...",
        required: true,
        options: ["فيسبوك", "تويتر", "VK", "Game Center", "Google Play"],
        sortOrder: 1,
        isActive: true
      },
      {
        id: generateId(),
        type: "text",
        name: "password",
        label: "كلمة المرور الخاصة بالحساب",
        placeholder: "أدخل كلمة المرور...",
        required: false,
        sortOrder: 2,
        isActive: true
      },
      {
        id: generateId(),
        type: "text",
        name: "whatsapp",
        label: "رقم الواتساب للتواصل",
        placeholder: "+966xxxxxxxxx",
        required: true,
        validation: {
          pattern: "^\\+[1-9]\\d{1,14}$"
        },
        sortOrder: 3,
        isActive: true
      }
    ],
    features: [
      "🚀 تسليم فوري للأكواد",
      "💯 ضمان الجودة والأمان",
      "🔒 معاملات آمنة ومشفرة",
      "📱 يعمل على جميع الأجهزة",
      "🎮 دعم فني متخصص",
      "💳 طرق دفع متعددة"
    ],
    tags: ["pubg", "mobile", "uc", "شحن", "ألعاب"],
    isActive: true,
    isFeatured: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  
  "free-fire": {
    id: "free-fire-001",
    name: "شحن جواهر Free Fire",
    description: "شحن فوري لجواهر Free Fire - احصل على الجواهر بأفضل الأسعار",
    category: "ألعاب الموبايل",
    productType: "digital",
    processingType: "instant",
    estimatedTime: "فوري",
    packages: [
      {
        id: generateId(),
        name: "100 جوهرة",
        amount: "100 💎",
        price: 10,
        originalPrice: 12,
        discount: 17,
        popular: false,
        isActive: true,
        sortOrder: 0,
        digitalCodes: []
      },
      {
        id: generateId(),
        name: "520 جوهرة",
        amount: "520 💎",
        price: 50,
        originalPrice: 60,
        discount: 17,
        popular: true,
        isActive: true,
        sortOrder: 1,
        digitalCodes: []
      },
      {
        id: generateId(),
        name: "1080 جوهرة",
        amount: "1080 💎",
        price: 100,
        originalPrice: 120,
        discount: 17,
        popular: false,
        isActive: true,
        sortOrder: 2,
        digitalCodes: []
      }
    ],
    fields: [
      {
        id: generateId(),
        type: "text",
        name: "player_id",
        label: "الايدي الخاص بك ID",
        placeholder: "أدخل معرف اللاعب...",
        required: true,
        validation: {
          minLength: 8,
          maxLength: 15
        },
        sortOrder: 0,
        isActive: true
      },
      {
        id: generateId(),
        type: "email",
        name: "email",
        label: "البريد الإلكتروني",
        placeholder: "<EMAIL>",
        required: true,
        sortOrder: 1,
        isActive: true
      },
      {
        id: generateId(),
        type: "text",
        name: "whatsapp",
        label: "رقم الواتساب للتواصل",
        placeholder: "+966xxxxxxxxx",
        required: true,
        sortOrder: 2,
        isActive: true
      }
    ],
    features: [
      "🚀 شحن فوري ومباشر",
      "💎 جواهر أصلية 100%",
      "🔒 آمن ومضمون",
      "📱 لجميع الأجهزة",
      "🎮 دعم فني 24/7"
    ],
    tags: ["free-fire", "diamonds", "شحن", "جواهر"],
    isActive: true,
    isFeatured: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

interface ModernProductPageProps {
  params: {
    slug: string
  }
}

export default function ModernProductPageRoute({ params }: ModernProductPageProps) {
  const router = useRouter()
  const product = mockProducts[params.slug]

  if (!product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">❌</div>
          <h3 className="text-xl font-bold text-white mb-2">المنتج غير موجود</h3>
          <p className="text-slate-400 mb-6">لم نتمكن من العثور على المنتج المطلوب</p>
          <button 
            onClick={() => router.push('/shop')}
            className="bg-yellow-500 hover:bg-yellow-600 text-slate-900 px-6 py-2 rounded-lg font-bold"
          >
            العودة للمتجر
          </button>
        </div>
      </div>
    )
  }

  const handlePurchase = async (formData: ProductFormData) => {
    console.log('Purchase data:', formData)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Redirect to success page or show success message
    alert(`تم الطلب بنجاح!\nالمنتج: ${product.name}\nالحزمة: ${formData.selectedPackage.name}\nالمجموع: ${formData.totalPrice} ${formData.currency}`)
    
    // In real app, redirect to checkout success
    // router.push('/checkout/success')
  }

  const handleBack = () => {
    router.back()
  }

  return (
    <ModernProductPage
      product={product}
      onPurchase={handlePurchase}
      onBack={handleBack}
      currency="USD"
    />
  )
}
