// =====================================================
// DEFAULT PRODUCT TEMPLATES
// =====================================================
// ## TODO: Replace with Supabase data loading
// These templates will be used to initialize the system with sample products

import { ProductTemplate, DynamicField, ProductPackage } from "@/lib/types"

/**
 * Generate unique ID for templates
 */
function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * PUBG Mobile UC Top-up Template
 */
export const pubgMobileTemplate: ProductTemplate = {
  id: "pubg-mobile-uc",
  name: "شحن يوسي PUBG Mobile",
  nameEnglish: "PUBG Mobile UC Top-up",
  description: "شحن فوري لعملة UC في لعبة PUBG Mobile - احصل على يوسي فوراً بأفضل الأسعار",
  descriptionEnglish: "Instant UC top-up for PUBG Mobile - Get your UC instantly at the best prices",
  category: "ألعاب الموبايل",
  basePrice: 25,
  estimatedTime: "فوري",
  productType: "digital",
  processingType: "instant",
  digitalConfig: {
    autoDeliver: true,
    codeType: "game_code",
    deliveryInstructions: "سيتم إرسال الكود إلى حسابك فوراً بعد الدفع. استخدم الكود في اللعبة لشحن UC.",
    expiryDays: 30
  },
  fields: [
    {
      id: generateId(),
      type: "text",
      name: "player_id",
      label: "معرف اللاعب",
      placeholder: "أدخل معرف اللاعب...",
      required: true,
      validation: {
        minLength: 8,
        maxLength: 12,
        pattern: "^[0-9]+$"
      },
      sortOrder: 0,
      isActive: true
    },
    {
      id: generateId(),
      type: "dropdown",
      name: "server",
      label: "الخادم",
      placeholder: "اختر الخادم...",
      required: true,
      options: ["الشرق الأوسط", "أوروبا", "آسيا", "أمريكا الشمالية", "أمريكا الجنوبية"],
      sortOrder: 1,
      isActive: true
    }
  ],
  packages: [
    {
      id: generateId(),
      name: "60 يوسي",
      amount: "60 UC",
      price: 5,
      originalPrice: 6,
      discount: 17,
      popular: false,
      isActive: true,
      sortOrder: 0,
      digitalCodes: [] // ## TODO: Add encrypted codes
    },
    {
      id: generateId(),
      name: "325 يوسي",
      amount: "325 UC",
      price: 25,
      originalPrice: 30,
      discount: 17,
      popular: true,
      isActive: true,
      sortOrder: 1,
      digitalCodes: [] // ## TODO: Add encrypted codes
    },
    {
      id: generateId(),
      name: "660 يوسي",
      amount: "660 UC",
      price: 50,
      originalPrice: 60,
      discount: 17,
      popular: false,
      isActive: true,
      sortOrder: 2,
      digitalCodes: [] // ## TODO: Add encrypted codes
    },
    {
      id: generateId(),
      name: "1800 يوسي",
      amount: "1800 UC",
      price: 120,
      originalPrice: 150,
      discount: 20,
      popular: false,
      isActive: true,
      sortOrder: 3,
      digitalCodes: [] // ## TODO: Add encrypted codes
    }
  ],
  features: [
    "🚀 تسليم فوري للأكواد",
    "💯 ضمان الجودة والأمان",
    "🔒 معاملات آمنة ومشفرة",
    "📱 يعمل على جميع الأجهزة",
    "🎮 دعم فني متخصص",
    "💳 طرق دفع متعددة"
  ],
  tags: ["pubg", "mobile", "uc", "شحن", "ألعاب"],
  isActive: true,
  isFeatured: true,
  createdAt: new Date(),
  updatedAt: new Date()
}

/**
 * Free Fire Diamonds Template
 */
export const freeFireTemplate: ProductTemplate = {
  id: "free-fire-diamonds",
  name: "شحن جواهر Free Fire",
  nameEnglish: "Free Fire Diamonds Top-up",
  description: "شحن فوري لجواهر Free Fire - احصل على الجواهر بأسرع وقت وأفضل الأسعار",
  descriptionEnglish: "Instant Free Fire Diamonds top-up - Get your diamonds quickly at the best prices",
  category: "ألعاب الموبايل",
  basePrice: 10,
  estimatedTime: "فوري",
  productType: "digital",
  processingType: "instant",
  digitalConfig: {
    autoDeliver: true,
    codeType: "game_code",
    deliveryInstructions: "سيتم شحن الجواهر مباشرة إلى حسابك في اللعبة خلال دقائق.",
    expiryDays: 7
  },
  fields: [
    {
      id: generateId(),
      type: "number",
      name: "player_id",
      label: "معرف اللاعب",
      labelEnglish: "Player ID",
      placeholder: "أدخل معرف اللاعب...",
      required: true,
      validation: {
        min: 100000000,
        max: 9999999999
      },
      sortOrder: 0,
      isActive: true
    }
  ],
  packages: [
    {
      id: generateId(),
      name: "100 جوهرة",
      amount: "100 💎",
      price: 10,
      originalPrice: 12,
      discount: 17,
      popular: false,
      isActive: true,
      sortOrder: 0,
      digitalCodes: []
    },
    {
      id: generateId(),
      name: "520 جوهرة",
      amount: "520 💎",
      price: 50,
      originalPrice: 60,
      discount: 17,
      popular: true,
      isActive: true,
      sortOrder: 1,
      digitalCodes: []
    },
    {
      id: generateId(),
      name: "1080 جوهرة",
      amount: "1080 💎",
      price: 100,
      originalPrice: 120,
      discount: 17,
      popular: false,
      isActive: true,
      sortOrder: 2,
      digitalCodes: []
    }
  ],
  features: [
    "🚀 شحن فوري ومباشر",
    "💎 جواهر أصلية 100%",
    "🔒 آمن ومضمون",
    "📱 لجميع الأجهزة",
    "🎮 دعم فني 24/7"
  ],
  tags: ["free fire", "diamonds", "جواهر", "شحن", "ألعاب"],
  isActive: true,
  isFeatured: true,
  createdAt: new Date(),
  updatedAt: new Date()
}

/**
 * Google Play Gift Card Template
 */
export const googlePlayTemplate: ProductTemplate = {
  id: "google-play-gift-card",
  name: "بطاقة هدايا Google Play",
  nameEnglish: "Google Play Gift Card",
  description: "بطاقات هدايا Google Play الرقمية - استخدمها لشراء التطبيقات والألعاب والمحتوى الرقمي",
  descriptionEnglish: "Digital Google Play Gift Cards - Use them to buy apps, games, and digital content",
  category: "بطاقات الهدايا",
  basePrice: 10,
  estimatedTime: "فوري",
  productType: "digital",
  processingType: "instant",
  digitalConfig: {
    autoDeliver: true,
    codeType: "coupon",
    deliveryInstructions: "استخدم الكود في متجر Google Play لإضافة الرصيد إلى حسابك.",
    expiryDays: 365
  },
  fields: [
    {
      id: generateId(),
      type: "email",
      name: "email",
      label: "البريد الإلكتروني",
      labelEnglish: "Email Address",
      placeholder: "أدخل بريدك الإلكتروني...",
      required: true,
      sortOrder: 0,
      isActive: true
    }
  ],
  packages: [
    {
      id: generateId(),
      name: "$10 USD",
      nameArabic: "10 دولار",
      amount: "$10 USD",
      price: 10,
      popular: false,
      isActive: true,
      sortOrder: 0,
      digitalCodes: []
    },
    {
      id: generateId(),
      name: "$25 USD",
      nameArabic: "25 دولار",
      amount: "$25 USD",
      price: 25,
      popular: true,
      isActive: true,
      sortOrder: 1,
      digitalCodes: []
    },
    {
      id: generateId(),
      name: "$50 USD",
      nameArabic: "50 دولار",
      amount: "$50 USD",
      price: 50,
      popular: false,
      isActive: true,
      sortOrder: 2,
      digitalCodes: []
    }
  ],
  features: [
    "🎁 بطاقة هدايا رقمية",
    "🚀 تسليم فوري",
    "🌍 صالحة عالمياً",
    "📱 لجميع أجهزة Android",
    "🔒 آمنة ومضمونة"
  ],
  tags: ["google play", "gift card", "بطاقة هدايا", "تطبيقات"],
  isActive: true,
  isFeatured: false,
  createdAt: new Date(),
  updatedAt: new Date()
}

/**
 * All default templates
 */
export const defaultTemplates: ProductTemplate[] = [
  pubgMobileTemplate,
  freeFireTemplate,
  googlePlayTemplate
]

/**
 * Initialize default templates in localStorage
 * ## TODO: Replace with Supabase initialization
 */
export function initializeDefaultTemplates(): void {
  const existingTemplates = localStorage.getItem('productTemplates')
  
  if (!existingTemplates) {
    localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates))
    console.log('Default product templates initialized')
  }
}

/**
 * Load product templates from localStorage
 * ## TODO: Replace with Supabase query
 */
export function loadProductTemplates(): ProductTemplate[] {
  try {
    const savedTemplates = localStorage.getItem('productTemplates')
    if (savedTemplates) {
      return JSON.parse(savedTemplates)
    }
    return []
  } catch (error) {
    console.error('Error loading product templates:', error)
    return []
  }
}

/**
 * Save product template to localStorage
 * ## TODO: Replace with Supabase insert/update
 */
export function saveProductTemplate(template: ProductTemplate): void {
  try {
    const templates = loadProductTemplates()
    const existingIndex = templates.findIndex(t => t.id === template.id)
    
    if (existingIndex >= 0) {
      templates[existingIndex] = template
    } else {
      templates.push(template)
    }
    
    localStorage.setItem('productTemplates', JSON.stringify(templates))
  } catch (error) {
    console.error('Error saving product template:', error)
    throw error
  }
}

/**
 * Delete product template from localStorage
 * ## TODO: Replace with Supabase delete
 */
export function deleteProductTemplate(templateId: string): void {
  try {
    const templates = loadProductTemplates()
    const filteredTemplates = templates.filter(t => t.id !== templateId)
    localStorage.setItem('productTemplates', JSON.stringify(filteredTemplates))
  } catch (error) {
    console.error('Error deleting product template:', error)
    throw error
  }
}
