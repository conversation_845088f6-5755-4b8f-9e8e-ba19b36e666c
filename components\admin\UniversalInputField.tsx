"use client"

import React, { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Eye, EyeOff, Settings, Check, X } from "lucide-react"
import { DynamicField, UniversalInputValidation } from "@/lib/types"

interface UniversalInputFieldProps {
  field: DynamicField
  value: any
  onChange: (value: any) => void
  error?: string
  disabled?: boolean
  isEditing?: boolean
  onFieldUpdate?: (updatedField: DynamicField) => void
}

export function UniversalInputField({ 
  field, 
  value, 
  onChange, 
  error, 
  disabled = false,
  isEditing = false,
  onFieldUpdate
}: UniversalInputFieldProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [isConfiguring, setIsConfiguring] = useState(false)
  const [tempField, setTempField] = useState<DynamicField>(field)

  // Get input type based on validation
  const getInputType = () => {
    if (field.inputValidation === "password" && !showPassword) return "password"
    if (field.inputValidation === "email") return "email"
    if (field.inputValidation === "number") return "number"
    if (field.inputValidation === "phone") return "tel"
    return "text"
  }

  // Get validation pattern
  const getValidationPattern = () => {
    switch (field.inputValidation) {
      case "email":
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      case "phone":
        return /^[\+]?[1-9][\d]{0,15}$/
      case "number":
        return /^\d+$/
      case "id":
        return /^[a-zA-Z0-9]+$/
      case "custom":
        return field.customPattern ? new RegExp(field.customPattern) : null
      default:
        return null
    }
  }

  // Validate input value
  const validateInput = (inputValue: string) => {
    if (!inputValue && field.required) {
      return "هذا الحقل مطلوب"
    }

    if (inputValue) {
      const pattern = getValidationPattern()
      if (pattern && !pattern.test(inputValue)) {
        switch (field.inputValidation) {
          case "email":
            return "يرجى إدخال بريد إلكتروني صحيح"
          case "phone":
            return "يرجى إدخال رقم هاتف صحيح"
          case "number":
            return "يرجى إدخال أرقام فقط"
          case "id":
            return "يرجى إدخال معرف صحيح (أرقام وحروف فقط)"
          case "custom":
            return "تنسيق الإدخال غير صحيح"
          default:
            return "قيمة غير صحيحة"
        }
      }

      // Length validation
      if (field.validation?.minLength && inputValue.length < field.validation.minLength) {
        return `يجب أن يكون الحد الأدنى ${field.validation.minLength} أحرف`
      }
      if (field.validation?.maxLength && inputValue.length > field.validation.maxLength) {
        return `يجب أن يكون الحد الأقصى ${field.validation.maxLength} أحرف`
      }

      // Number range validation
      if (field.inputValidation === "number") {
        const numValue = parseInt(inputValue)
        if (field.validation?.min && numValue < field.validation.min) {
          return `القيمة يجب أن تكون أكبر من ${field.validation.min}`
        }
        if (field.validation?.max && numValue > field.validation.max) {
          return `القيمة يجب أن تكون أقل من ${field.validation.max}`
        }
      }
    }

    return null
  }

  // Handle input change with validation
  const handleInputChange = (inputValue: string) => {
    onChange(inputValue)
  }

  // Format placeholder based on validation type
  const getPlaceholder = () => {
    if (field.placeholder) return field.placeholder

    switch (field.inputValidation) {
      case "email":
        return "<EMAIL>"
      case "phone":
        return "+249123456789"
      case "number":
        return "123456"
      case "id":
        return "ABC123DEF"
      case "password":
        return "••••••••"
      default:
        return `أدخل ${field.label}...`
    }
  }

  // Save field configuration
  const saveFieldConfig = () => {
    if (onFieldUpdate) {
      onFieldUpdate(tempField)
    }
    setIsConfiguring(false)
  }

  // Cancel field configuration
  const cancelFieldConfig = () => {
    setTempField(field)
    setIsConfiguring(false)
  }

  const validationError = error || validateInput(value || "")

  return (
    <div className="space-y-2">
      {/* Field Label and Configuration */}
      <div className="flex items-center justify-between">
        <Label className="text-slate-300 flex items-center gap-2">
          {field.label}
          {field.required && <span className="text-red-400">*</span>}
          
          {/* Validation Type Badge */}
          <Badge 
            variant="outline" 
            className="text-xs border-slate-600 text-slate-400"
          >
            {field.inputValidation || "text"}
          </Badge>
        </Label>

        {/* Configuration Button (Admin Only) */}
        {isEditing && (
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsConfiguring(true)}
            className="h-6 w-6 p-0 text-slate-400 hover:text-white"
          >
            <Settings className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Input Field */}
      <div className="relative">
        <Input
          type={getInputType()}
          value={value || ""}
          onChange={(e) => handleInputChange(e.target.value)}
          placeholder={getPlaceholder()}
          disabled={disabled}
          className={`bg-slate-600 border-slate-500 text-white ${
            validationError ? "border-red-500" : ""
          }`}
        />

        {/* Password Toggle */}
        {field.inputValidation === "password" && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute left-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-slate-400 hover:text-white"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
          </Button>
        )}
      </div>

      {/* Validation Error */}
      {validationError && (
        <p className="text-red-400 text-sm">{validationError}</p>
      )}

      {/* Field Configuration Modal */}
      {isConfiguring && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-slate-800 border border-slate-700 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-white font-bold mb-4">تكوين الحقل</h3>
            
            <div className="space-y-4">
              {/* Field Label */}
              <div>
                <Label className="text-slate-300">تسمية الحقل</Label>
                <Input
                  value={tempField.label}
                  onChange={(e) => setTempField(prev => ({ ...prev, label: e.target.value }))}
                  className="bg-slate-700 border-slate-600 text-white"
                  placeholder="مثال: معرف اللاعب"
                />
              </div>

              {/* Validation Type */}
              <div>
                <Label className="text-slate-300">نوع التحقق</Label>
                <Select
                  value={tempField.inputValidation || "text"}
                  onValueChange={(value: UniversalInputValidation) => 
                    setTempField(prev => ({ ...prev, inputValidation: value }))
                  }
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="text">نص عادي</SelectItem>
                    <SelectItem value="email">بريد إلكتروني</SelectItem>
                    <SelectItem value="phone">رقم هاتف</SelectItem>
                    <SelectItem value="number">أرقام فقط</SelectItem>
                    <SelectItem value="id">معرف (أرقام وحروف)</SelectItem>
                    <SelectItem value="password">كلمة مرور</SelectItem>
                    <SelectItem value="custom">نمط مخصص</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Placeholder */}
              <div>
                <Label className="text-slate-300">النص التوضيحي</Label>
                <Input
                  value={tempField.placeholder || ""}
                  onChange={(e) => setTempField(prev => ({ ...prev, placeholder: e.target.value }))}
                  className="bg-slate-700 border-slate-600 text-white"
                  placeholder="مثال: أدخل معرف اللاعب..."
                />
              </div>

              {/* Required Toggle */}
              <div className="flex items-center justify-between">
                <Label className="text-slate-300">حقل مطلوب</Label>
                <input
                  type="checkbox"
                  checked={tempField.required}
                  onChange={(e) => setTempField(prev => ({ ...prev, required: e.target.checked }))}
                  className="rounded"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 mt-6">
              <Button onClick={saveFieldConfig} className="flex-1 bg-green-600 hover:bg-green-700">
                <Check className="h-4 w-4 mr-2" />
                حفظ
              </Button>
              <Button onClick={cancelFieldConfig} variant="outline" className="flex-1 border-slate-600 text-slate-300">
                <X className="h-4 w-4 mr-2" />
                إلغاء
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
