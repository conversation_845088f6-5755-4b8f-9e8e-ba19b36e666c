"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Package,
  Zap,
  Clock,
  Star,
  BarChart3
} from "lucide-react"
import { ProductTemplate, ProductFilters, ProductStats } from "@/lib/types"
import { getProducts, getProductStats, deleteProduct } from "@/lib/services/productService"
import { ProductForm } from "./ProductForm"
import { formatCurrency } from "@/lib/data/currencies"

export function ProductDashboard() {
  // ## TODO: Add user authentication check
  // ## TODO: Implement real-time updates with Supabase subscriptions
  
  const [products, setProducts] = useState<ProductTemplate[]>([])
  const [filteredProducts, setFilteredProducts] = useState<ProductTemplate[]>([])
  const [stats, setStats] = useState<ProductStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [filters, setFilters] = useState<ProductFilters>({})
  const [selectedProduct, setSelectedProduct] = useState<ProductTemplate | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  // Load products and stats on component mount
  useEffect(() => {
    loadData()
  }, [])

  // Apply filters when search or filters change
  useEffect(() => {
    applyFilters()
  }, [products, searchQuery, filters])

  /**
   * ## TODO: Replace with Supabase real-time subscription
   * Load products and statistics
   */
  const loadData = async () => {
    try {
      setIsLoading(true)
      const [productsData, statsData] = await Promise.all([
        getProducts().catch(err => {
          console.error("Error loading products:", err)
          return []
        }),
        getProductStats().catch(err => {
          console.error("Error loading stats:", err)
          return {
            totalProducts: 0,
            activeProducts: 0,
            digitalProducts: 0,
            physicalProducts: 0,
            totalPackages: 0,
            totalOrders: 0,
            popularCategories: []
          }
        })
      ])
      setProducts(Array.isArray(productsData) ? productsData : [])
      setStats(statsData)
    } catch (error) {
      console.error("Error loading data:", error)
      // Set fallback data
      setProducts([])
      setStats({
        totalProducts: 0,
        activeProducts: 0,
        digitalProducts: 0,
        physicalProducts: 0,
        totalPackages: 0,
        totalOrders: 0,
        popularCategories: []
      })
      // ## TODO: Show error toast notification
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Apply search and filters to products
   */
  const applyFilters = () => {
    // Ensure products is a valid array
    const validProducts = Array.isArray(products) ? products.filter(p => p && p.name && p.category) : []
    let filtered = [...validProducts]

    // Apply search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(product =>
        product.name?.toLowerCase().includes(query) ||
        product.description?.toLowerCase().includes(query) ||
        product.category?.toLowerCase().includes(query)
      )
    }

    // Apply filters
    if (filters.category) {
      filtered = filtered.filter(p => p.category === filters.category)
    }
    if (filters.productType) {
      filtered = filtered.filter(p => p.productType === filters.productType)
    }
    if (filters.processingType) {
      filtered = filtered.filter(p => p.processingType === filters.processingType)
    }
    if (filters.isActive !== undefined) {
      filtered = filtered.filter(p => p.isActive === filters.isActive)
    }

    setFilteredProducts(filtered)
  }

  /**
   * Handle product creation
   */
  const handleProductCreate = async (product: ProductTemplate) => {
    setProducts(prev => [...prev, product])
    setIsCreateDialogOpen(false)
    await loadData() // Refresh stats
  }

  /**
   * Handle product update
   */
  const handleProductUpdate = async (product: ProductTemplate) => {
    setProducts(prev => prev.map(p => p.id === product.id ? product : p))
    setIsEditDialogOpen(false)
    setSelectedProduct(null)
    await loadData() // Refresh stats
  }

  /**
   * Handle product deletion
   */
  const handleProductDelete = async (productId: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا المنتج؟")) return
    
    try {
      await deleteProduct(productId)
      setProducts(prev => prev.filter(p => p.id !== productId))
      await loadData() // Refresh stats
    } catch (error) {
      console.error("Error deleting product:", error)
      // ## TODO: Show error toast notification
    }
  }

  /**
   * Get unique categories for filter dropdown
   */
  const getCategories = () => {
    const categories = [...new Set(products.map(p => p.category))]
    return categories.sort()
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-white">جاري التحميل...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6 p-4 lg:p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold text-white">إدارة المنتجات</h1>
          <p className="text-slate-400 mt-1">إنشاء وتعديل وإدارة منتجات المتجر</p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              إضافة منتج جديد
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>إنشاء منتج جديد</DialogTitle>
            </DialogHeader>
            <ProductForm
              onSave={handleProductCreate}
              onCancel={() => setIsCreateDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">إجمالي المنتجات</p>
                  <p className="text-2xl font-bold text-white">{stats.totalProducts}</p>
                </div>
                <Package className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">المنتجات النشطة</p>
                  <p className="text-2xl font-bold text-green-400">{stats.activeProducts}</p>
                </div>
                <Zap className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">المنتجات الرقمية</p>
                  <p className="text-2xl font-bold text-purple-400">{stats.digitalProducts}</p>
                </div>
                <Star className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">إجمالي الحزم</p>
                  <p className="text-2xl font-bold text-yellow-400">{stats.totalPackages}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-yellow-400" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Filters */}
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="البحث في المنتجات..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white"
              />
            </div>

            {/* Filters */}
            <div className="flex gap-2">
              <Select value={filters.category || "all"} onValueChange={(value) =>
                setFilters(prev => ({ ...prev, category: value === "all" ? undefined : value }))
              }>
                <SelectTrigger className="w-40 bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="الفئة" />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  {getCategories().map(category => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={filters.productType || "all"} onValueChange={(value) =>
                setFilters(prev => ({ ...prev, productType: value === "all" ? undefined : value as any }))
              }>
                <SelectTrigger className="w-40 bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="النوع" />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  <SelectItem value="all">جميع الأنواع</SelectItem>
                  <SelectItem value="digital">رقمي</SelectItem>
                  <SelectItem value="physical">مادي</SelectItem>
                  <SelectItem value="service">خدمة</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProducts.map((product) => (
          <Card key={product.id} className="bg-slate-800/50 border-slate-700/50 hover:border-slate-600 transition-colors">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-white text-lg mb-2">{product.name}</CardTitle>
                  <div className="flex gap-2 mb-2">
                    <Badge variant={product.isActive ? "default" : "secondary"}>
                      {product.isActive ? "نشط" : "غير نشط"}
                    </Badge>
                    <Badge variant="outline" className={
                      product.productType === "digital" ? "border-purple-500 text-purple-400" :
                      product.productType === "physical" ? "border-blue-500 text-blue-400" :
                      "border-green-500 text-green-400"
                    }>
                      {product.productType === "digital" ? "رقمي" :
                       product.productType === "physical" ? "مادي" : "خدمة"}
                    </Badge>
                    {product.processingType === "instant" && (
                      <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                        <Zap className="h-3 w-3 mr-1" />
                        فوري
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <p className="text-slate-400 text-sm mb-3 line-clamp-2">
                {product.description || "لا يوجد وصف"}
              </p>
              
              <div className="flex justify-between items-center text-sm text-slate-400 mb-4">
                <span>الفئة: {product.category}</span>
                <span>{product.packages?.length || 0} حزمة</span>
              </div>

              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setSelectedProduct(product)
                    setIsEditDialogOpen(true)
                  }}
                  className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  تعديل
                </Button>
                
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleProductDelete(product.id)}
                  className="border-red-600 text-red-400 hover:bg-red-600/10"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredProducts.length === 0 && !isLoading && (
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-8 text-center">
            <Package className="h-16 w-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">لا توجد منتجات</h3>
            <p className="text-slate-400 mb-4">
              {searchQuery || Object.keys(filters).length > 0 
                ? "لم يتم العثور على منتجات تطابق البحث أو الفلاتر"
                : "ابدأ بإنشاء منتجك الأول"
              }
            </p>
            {!searchQuery && Object.keys(filters).length === 0 && (
              <Button 
                onClick={() => setIsCreateDialogOpen(true)}
                className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                إضافة منتج جديد
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Edit Product Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تعديل المنتج</DialogTitle>
          </DialogHeader>
          {selectedProduct && (
            <ProductForm
              product={selectedProduct}
              onSave={handleProductUpdate}
              onCancel={() => {
                setIsEditDialogOpen(false)
                setSelectedProduct(null)
              }}
              isEditing
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
