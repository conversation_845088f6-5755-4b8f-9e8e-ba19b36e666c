"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import {
  Eye,
  EyeOff,
  Copy,
  Download,
  Gift,
  Key,
  Shield,
  Clock,
  CheckCircle,
  AlertTriangle,
  Package
} from "lucide-react"
import { DigitalCode } from "@/lib/types"
import { decryptCode, formatCodeForDisplay } from "@/lib/utils/encryption"

interface UserOrder {
  id: string
  productName: string
  productIcon?: string
  packageName: string
  price: number
  currency: string
  status: "pending" | "completed" | "failed"
  deliveryType: "direct_charge" | "code_based"
  createdAt: Date
  completedAt?: Date
  
  // For direct charge orders
  playerInfo?: {
    playerId: string
    serverRegion?: string
    email?: string
  }
  
  // For code-based orders
  assignedCodes?: DigitalCode[]
}

interface CodeRevealInterfaceProps {
  userId: string
  orders?: UserOrder[]
}

export function CodeRevealInterface({ userId, orders = [] }: CodeRevealInterfaceProps) {
  const [revealedCodes, setRevealedCodes] = useState<Set<string>>(new Set())
  const [copiedCodes, setCopiedCodes] = useState<Set<string>>(new Set())

  // Load revealed codes from localStorage
  useEffect(() => {
    const storageKey = `revealed_codes_${userId}`
    const stored = JSON.parse(localStorage.getItem(storageKey) || '[]')
    setRevealedCodes(new Set(stored))
  }, [userId])

  // Save revealed codes to localStorage
  const saveRevealedCodes = (codeIds: Set<string>) => {
    const storageKey = `revealed_codes_${userId}`
    localStorage.setItem(storageKey, JSON.stringify(Array.from(codeIds)))
    setRevealedCodes(codeIds)
  }

  // Reveal code
  const handleRevealCode = (codeId: string) => {
    const newRevealed = new Set(revealedCodes)
    newRevealed.add(codeId)
    saveRevealedCodes(newRevealed)
    
    // Update code's revealedAt timestamp (in real app, this would be API call)
    // For demo, we'll just track locally
  }

  // Copy code to clipboard
  const handleCopyCode = async (code: DigitalCode) => {
    try {
      const decryptedCode = decryptCode(code.code)
      await navigator.clipboard.writeText(decryptedCode)
      
      // Show copied feedback
      const newCopied = new Set(copiedCodes)
      newCopied.add(code.id)
      setCopiedCodes(newCopied)
      
      // Remove copied feedback after 2 seconds
      setTimeout(() => {
        setCopiedCodes(prev => {
          const updated = new Set(prev)
          updated.delete(code.id)
          return updated
        })
      }, 2000)
      
    } catch (error) {
      alert("فشل في نسخ الكود")
    }
  }

  // Get code display
  const getCodeDisplay = (code: DigitalCode, isRevealed: boolean) => {
    if (isRevealed) {
      try {
        const decryptedCode = decryptCode(code.code)
        return formatCodeForDisplay(decryptedCode)
      } catch {
        return "خطأ في فك التشفير"
      }
    }
    return "••••••••••••"
  }

  // Get order status color
  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-500/20 text-green-400"
      case "pending": return "bg-yellow-500/20 text-yellow-400"
      case "failed": return "bg-red-500/20 text-red-400"
      default: return "bg-slate-500/20 text-slate-400"
    }
  }

  const getOrderStatusText = (status: string) => {
    switch (status) {
      case "completed": return "مكتمل"
      case "pending": return "قيد المعالجة"
      case "failed": return "فشل"
      default: return "غير معروف"
    }
  }

  // Filter orders by type
  const codeBasedOrders = orders.filter(order => 
    order.deliveryType === "code_based" && order.status === "completed"
  )
  const directChargeOrders = orders.filter(order => 
    order.deliveryType === "direct_charge"
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-slate-800/50 border-slate-700/50">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Gift className="h-5 w-5" />
            محفظة الأكواد والطلبات
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Code-Based Orders */}
      {codeBasedOrders.length > 0 && (
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Key className="h-5 w-5" />
              الأكواد الرقمية ({codeBasedOrders.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {codeBasedOrders.map((order) => (
              <Card key={order.id} className="bg-slate-700/50 border-slate-600">
                <CardContent className="p-4">
                  {/* Order Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{order.productIcon || "🎮"}</div>
                      <div>
                        <h4 className="text-white font-medium">{order.productName}</h4>
                        <p className="text-slate-400 text-sm">{order.packageName}</p>
                        <p className="text-slate-500 text-xs">
                          {order.completedAt?.toLocaleDateString('ar')} • ${order.price}
                        </p>
                      </div>
                    </div>
                    <Badge className={getOrderStatusColor(order.status)}>
                      {getOrderStatusText(order.status)}
                    </Badge>
                  </div>

                  {/* Assigned Codes */}
                  {order.assignedCodes && order.assignedCodes.length > 0 && (
                    <div className="space-y-3">
                      <h5 className="text-slate-300 font-medium text-sm flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        الأكواد المخصصة ({order.assignedCodes.length})
                      </h5>
                      
                      {order.assignedCodes.map((code) => {
                        const isRevealed = revealedCodes.has(code.id)
                        const isCopied = copiedCodes.has(code.id)
                        
                        return (
                          <div
                            key={code.id}
                            className="bg-slate-600/50 rounded-lg p-3 border border-slate-500"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="text-white font-mono text-sm mb-1">
                                  {getCodeDisplay(code, isRevealed)}
                                </div>
                                {code.expiresAt && (
                                  <div className="flex items-center gap-1 text-slate-400 text-xs">
                                    <Clock className="h-3 w-3" />
                                    <span>ينتهي: {code.expiresAt.toLocaleDateString('ar')}</span>
                                  </div>
                                )}
                              </div>
                              
                              <div className="flex items-center gap-2">
                                {!isRevealed ? (
                                  <Button
                                    size="sm"
                                    onClick={() => handleRevealCode(code.id)}
                                    className="bg-blue-600 hover:bg-blue-700"
                                  >
                                    <Eye className="h-3 w-3 mr-1" />
                                    كشف الكود
                                  </Button>
                                ) : (
                                  <>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => handleCopyCode(code)}
                                      className={`border-slate-500 ${
                                        isCopied 
                                          ? 'bg-green-600/20 text-green-400 border-green-500' 
                                          : 'text-slate-300 hover:bg-slate-700'
                                      }`}
                                    >
                                      {isCopied ? (
                                        <>
                                          <CheckCircle className="h-3 w-3 mr-1" />
                                          تم النسخ
                                        </>
                                      ) : (
                                        <>
                                          <Copy className="h-3 w-3 mr-1" />
                                          نسخ
                                        </>
                                      )}
                                    </Button>
                                    
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => {
                                        const newRevealed = new Set(revealedCodes)
                                        newRevealed.delete(code.id)
                                        saveRevealedCodes(newRevealed)
                                      }}
                                      className="border-slate-500 text-slate-300 hover:bg-slate-700"
                                    >
                                      <EyeOff className="h-3 w-3" />
                                    </Button>
                                  </>
                                )}
                              </div>
                            </div>
                            
                            {/* Code Instructions */}
                            {isRevealed && (
                              <div className="mt-3 pt-3 border-t border-slate-500">
                                <div className="bg-blue-500/20 border border-blue-500/30 rounded p-2">
                                  <p className="text-blue-300 text-xs">
                                    💡 <strong>كيفية الاستخدام:</strong> انسخ الكود واستخدمه في التطبيق أو الموقع المحدد. 
                                    احتفظ بالكود في مكان آمن ولا تشاركه مع أحد.
                                  </p>
                                </div>
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Direct Charge Orders */}
      {directChargeOrders.length > 0 && (
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Package className="h-5 w-5" />
              طلبات الشحن المباشر ({directChargeOrders.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {directChargeOrders.map((order) => (
              <Card key={order.id} className="bg-slate-700/50 border-slate-600">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{order.productIcon || "⚡"}</div>
                      <div>
                        <h4 className="text-white font-medium">{order.productName}</h4>
                        <p className="text-slate-400 text-sm">{order.packageName}</p>
                        <p className="text-slate-500 text-xs">
                          {order.createdAt.toLocaleDateString('ar')} • ${order.price}
                        </p>
                        {order.playerInfo && (
                          <p className="text-slate-400 text-xs">
                            معرف اللاعب: {order.playerInfo.playerId}
                          </p>
                        )}
                      </div>
                    </div>
                    <Badge className={getOrderStatusColor(order.status)}>
                      {getOrderStatusText(order.status)}
                    </Badge>
                  </div>
                  
                  {order.status === "completed" && (
                    <div className="mt-3 pt-3 border-t border-slate-600">
                      <div className="bg-green-500/20 border border-green-500/30 rounded p-2">
                        <p className="text-green-300 text-xs">
                          ✅ تم الشحن بنجاح إلى حسابك. تحقق من اللعبة لرؤية الرصيد الجديد.
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {orders.length === 0 && (
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-8 text-center">
            <Gift className="h-16 w-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">لا توجد طلبات</h3>
            <p className="text-slate-400 mb-4">
              لم تقم بأي عمليات شراء بعد. ابدأ بتصفح المنتجات المتاحة.
            </p>
            <Button className="bg-blue-600 hover:bg-blue-700">
              تصفح المنتجات
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Security Notice */}
      <Card className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border-yellow-500/30">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Shield className="h-5 w-5 text-yellow-400 mt-0.5" />
            <div>
              <h4 className="text-yellow-300 font-medium mb-1">تنبيه أمني</h4>
              <p className="text-yellow-200 text-sm">
                • لا تشارك أكوادك مع أي شخص آخر
                <br />
                • استخدم الأكواد فور الحصول عليها
                <br />
                • احتفظ بلقطة شاشة للأكواد كنسخة احتياطية
                <br />
                • تواصل مع الدعم الفني في حالة وجود مشاكل
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
