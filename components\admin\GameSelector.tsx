"use client"

import React, { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, Gamepad2, Star, TrendingUp } from "lucide-react"

interface GameInfo {
  id: string
  name: string
  nameEnglish: string
  icon: string
  category: string
  popular: boolean
  trending: boolean
  description: string
  supportedTypes: ("direct_charge" | "code_based")[]
}

interface GameSelectorProps {
  selectedGame?: GameInfo
  onGameSelect: (game: GameInfo) => void
  productType?: "direct_charge" | "code_based"
  disabled?: boolean
}

const availableGames: GameInfo[] = [
  {
    id: "pubg_mobile",
    name: "PUBG Mobile",
    nameEnglish: "PUBG Mobile",
    icon: "🎮",
    category: "باتل رويال",
    popular: true,
    trending: true,
    description: "شحن UC لـ PUBG Mobile",
    supportedTypes: ["direct_charge", "code_based"]
  },
  {
    id: "free_fire",
    name: "Free Fire",
    nameEnglish: "Free Fire",
    icon: "🔥",
    category: "باتل رويال",
    popular: true,
    trending: false,
    description: "شحن جواهر Free Fire",
    supportedTypes: ["direct_charge", "code_based"]
  },
  {
    id: "call_of_duty",
    name: "Call of Duty Mobile",
    nameEnglish: "Call of Duty Mobile",
    icon: "⚔️",
    category: "أكشن",
    popular: true,
    trending: false,
    description: "شحن CP لـ Call of Duty Mobile",
    supportedTypes: ["direct_charge", "code_based"]
  },
  {
    id: "tiktok",
    name: "TikTok",
    nameEnglish: "TikTok",
    icon: "🎵",
    category: "وسائل التواصل",
    popular: false,
    trending: true,
    description: "شحن عملات TikTok",
    supportedTypes: ["direct_charge"]
  },
  {
    id: "google_play",
    name: "Google Play",
    nameEnglish: "Google Play",
    icon: "🎁",
    category: "بطاقات هدايا",
    popular: true,
    trending: false,
    description: "بطاقات هدايا Google Play",
    supportedTypes: ["code_based"]
  },
  {
    id: "itunes",
    name: "iTunes",
    nameEnglish: "iTunes",
    icon: "🍎",
    category: "بطاقات هدايا",
    popular: true,
    trending: false,
    description: "بطاقات هدايا iTunes",
    supportedTypes: ["code_based"]
  },
  {
    id: "steam",
    name: "Steam",
    nameEnglish: "Steam",
    icon: "🎮",
    category: "ألعاب PC",
    popular: false,
    trending: false,
    description: "بطاقات هدايا Steam",
    supportedTypes: ["code_based"]
  },
  {
    id: "netflix",
    name: "Netflix",
    nameEnglish: "Netflix",
    icon: "🎬",
    category: "ترفيه",
    popular: false,
    trending: false,
    description: "بطاقات هدايا Netflix",
    supportedTypes: ["code_based"]
  }
]

export function GameSelector({ 
  selectedGame, 
  onGameSelect, 
  productType,
  disabled = false 
}: GameSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("")

  // Filter games based on search and product type
  const filteredGames = availableGames.filter(game => {
    const matchesSearch = game.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         game.nameEnglish.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         game.category.toLowerCase().includes(searchTerm.toLowerCase())
    
    const supportsProductType = !productType || game.supportedTypes.includes(productType)
    
    return matchesSearch && supportsProductType
  })

  // Group games by category
  const gamesByCategory = filteredGames.reduce((acc, game) => {
    if (!acc[game.category]) {
      acc[game.category] = []
    }
    acc[game.category].push(game)
    return acc
  }, {} as Record<string, GameInfo[]>)

  return (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h3 className="text-white font-bold text-lg mb-2">اختر اللعبة أو الخدمة</h3>
        <p className="text-slate-400 text-sm">
          حدد اللعبة أو الخدمة التي تريد إنشاء منتج لها
        </p>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
        <Input
          type="text"
          placeholder="ابحث عن لعبة أو خدمة..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="bg-slate-700 border-slate-600 text-white pr-10"
        />
      </div>

      {/* Product Type Filter Info */}
      {productType && (
        <div className="p-3 bg-blue-500/20 border border-blue-500/30 rounded-lg">
          <p className="text-blue-300 text-sm">
            {productType === "direct_charge" 
              ? "🔄 عرض الألعاب التي تدعم الشحن المباشر"
              : "🎁 عرض المنتجات التي تدعم الأكواد الرقمية"
            }
          </p>
        </div>
      )}

      {/* Games Grid */}
      <div className="space-y-6">
        {Object.entries(gamesByCategory).map(([category, games]) => (
          <div key={category}>
            <h4 className="text-white font-medium mb-3 flex items-center gap-2">
              <Gamepad2 className="h-4 w-4" />
              {category}
            </h4>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {games.map((game) => {
                const isSelected = selectedGame?.id === game.id
                
                return (
                  <Card
                    key={game.id}
                    className={`cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                      isSelected
                        ? 'border-2 border-yellow-400 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 shadow-xl shadow-yellow-400/20'
                        : 'border border-slate-600 bg-slate-800/50 hover:border-slate-500'
                    } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => !disabled && onGameSelect(game)}
                  >
                    <CardContent className="p-4 text-center">
                      {/* Game Icon */}
                      <div className="text-4xl mb-2">{game.icon}</div>
                      
                      {/* Game Name */}
                      <h5 className="text-white font-medium text-sm mb-1">{game.name}</h5>
                      <p className="text-slate-400 text-xs mb-2">{game.nameEnglish}</p>
                      
                      {/* Description */}
                      <p className="text-slate-300 text-xs mb-3">{game.description}</p>
                      
                      {/* Badges */}
                      <div className="flex flex-wrap gap-1 justify-center mb-2">
                        {game.popular && (
                          <Badge className="bg-yellow-500/20 text-yellow-400 text-xs">
                            <Star className="h-2 w-2 mr-1" />
                            شائع
                          </Badge>
                        )}
                        {game.trending && (
                          <Badge className="bg-green-500/20 text-green-400 text-xs">
                            <TrendingUp className="h-2 w-2 mr-1" />
                            رائج
                          </Badge>
                        )}
                      </div>
                      
                      {/* Supported Types */}
                      <div className="text-xs text-slate-400">
                        {game.supportedTypes.includes("direct_charge") && "🔄 "}
                        {game.supportedTypes.includes("code_based") && "🎁 "}
                      </div>
                      
                      {/* Selection Indicator */}
                      {isSelected && (
                        <div className="mt-2 pt-2 border-t border-yellow-400/30">
                          <div className="w-4 h-4 bg-yellow-400 rounded-full mx-auto flex items-center justify-center">
                            <div className="w-1.5 h-1.5 bg-slate-900 rounded-full"></div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>
        ))}
      </div>

      {/* No Results */}
      {filteredGames.length === 0 && (
        <div className="text-center py-8">
          <Gamepad2 className="h-16 w-16 text-slate-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">لا توجد نتائج</h3>
          <p className="text-slate-400 mb-4">
            {searchTerm 
              ? `لم يتم العثور على ألعاب تحتوي على "${searchTerm}"`
              : "لا توجد ألعاب متاحة لهذا النوع من المنتجات"
            }
          </p>
        </div>
      )}

      {/* Selected Game Summary */}
      {selectedGame && (
        <div className="mt-6 p-4 bg-slate-700/50 rounded-lg border border-slate-600">
          <div className="flex items-center gap-3">
            <div className="text-2xl">{selectedGame.icon}</div>
            <div>
              <p className="text-white font-medium">{selectedGame.name}</p>
              <p className="text-slate-400 text-sm">{selectedGame.description}</p>
              <div className="flex gap-2 mt-1">
                {selectedGame.supportedTypes.map(type => (
                  <Badge 
                    key={type}
                    className="text-xs bg-slate-600 text-slate-300"
                  >
                    {type === "direct_charge" ? "شحن مباشر" : "أكواد رقمية"}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
